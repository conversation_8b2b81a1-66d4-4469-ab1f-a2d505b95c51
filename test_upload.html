<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload</title>
</head>
<body>
    <h1>Testing Data Upload</h1>
    <div id="test-results"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>
    <script src="neural-network.js"></script>
    <script src="data-processor.js"></script>
    <script src="multi-model-manager.js"></script>
    <script src="advanced-analytics.js"></script>
    
    <script>
        // Test the data processing pipeline
        async function testDataUpload() {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                resultsDiv.innerHTML += '<p>🔄 Starting test...</p>';
                
                // Create instances
                const dataProcessor = new DataProcessor();
                const multiModelManager = new MultiModelManager();
                const advancedAnalytics = new AdvancedAnalytics(multiModelManager, dataProcessor);
                
                resultsDiv.innerHTML += '<p>✅ Instances created successfully</p>';
                
                // Load and test CSV data
                const response = await fetch('data.csv');
                const csvText = await response.text();
                
                resultsDiv.innerHTML += '<p>✅ CSV file loaded successfully</p>';
                resultsDiv.innerHTML += `<p>📊 CSV size: ${csvText.length} characters</p>`;
                
                // Process the data
                const processedData = dataProcessor.processData(csvText);
                
                resultsDiv.innerHTML += '<p>✅ Data processed successfully</p>';
                resultsDiv.innerHTML += `<p>📊 Processed ${processedData.X.length} samples with ${processedData.X[0].length} features</p>';
                
                // Test multi-model data preparation
                multiModelManager.prepareTrainingData(processedData);
                
                resultsDiv.innerHTML += '<p>✅ Multi-model data preparation successful</p>';
                
                // Get model status
                const status = multiModelManager.getModelStatus();
                
                resultsDiv.innerHTML += '<p>✅ Model status retrieved successfully</p>';
                
                // Display data distribution
                Object.keys(status).forEach(interval => {
                    const count = status[interval].dataCount;
                    resultsDiv.innerHTML += `<p>📈 Interval ${interval}%: ${count} samples</p>`;
                });
                
                // Test analytics
                advancedAnalytics.updateAnalyticsDisplays(processedData);
                
                resultsDiv.innerHTML += '<p>✅ Advanced analytics updated successfully</p>';
                
                resultsDiv.innerHTML += '<p>🎉 All tests passed! The application should work correctly.</p>';
                
            } catch (error) {
                resultsDiv.innerHTML += `<p>❌ Error: ${error.message}</p>`;
                resultsDiv.innerHTML += `<p>🔍 Stack trace: ${error.stack}</p>`;
                console.error('Test error:', error);
            }
        }
        
        // Run test when page loads
        window.addEventListener('load', testDataUpload);
    </script>
</body>
</html>
