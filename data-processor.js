// Data Processing Module for Heat Exchanger Fouling Prediction

class DataProcessor {
    constructor() {
        this.scaler = null;
        this.processedData = null;
        this.originalData = null;
        this.features = [
            'UNIT CHARGE',
            'REACTOR EFFLUENT FLOW', 
            'Temp(In)_Shell',
            'Temp(Out)_Shell',
            'REACTOR FEED FLOW',
            'Temp(In)_Tube',
            'Temp(Out)_Tube'
        ];
        this.target = 'Fouling Resistance';
    }

    parseCSV(csvText) {
        try {
            const result = Papa.parse(csvText, {
                header: true,
                skipEmptyLines: true,
                dynamicTyping: true,
                transformHeader: (header) => {
                    // Clean up header names
                    return header.trim().replace(/[^\w\s()]/g, '');
                }
            });

            if (result.errors.length > 0) {
                throw new Error(`CSV parsing errors: ${result.errors.map(e => e.message).join(', ')}`);
            }

            return result.data;
        } catch (error) {
            throw new Error(`Failed to parse CSV: ${error.message}`);
        }
    }

    validateData(data) {
        if (!data || data.length === 0) {
            throw new Error('No data found in CSV file');
        }

        const headers = Object.keys(data[0]);
        console.log('Available headers:', headers);

        // Check for exact matches first, then partial matches
        const missingFeatures = this.features.filter(feature => {
            const exactMatch = headers.includes(feature);
            const partialMatch = headers.some(header =>
                header.includes(feature.replace(/[()_]/g, '')) ||
                feature.replace(/[()_]/g, '').includes(header.replace(/[()_]/g, ''))
            );
            return !exactMatch && !partialMatch;
        });

        if (missingFeatures.length > 0) {
            throw new Error(`Missing required columns: ${missingFeatures.join(', ')}`);
        }

        // Check if target column exists
        const targetExists = headers.some(header =>
            header.includes('Fouling') && header.includes('Resistance')
        );

        if (!targetExists) {
            throw new Error('Missing target column: Fouling Resistance');
        }

        return true;
    }

    cleanData(data) {
        const cleaned = [];
        
        for (const row of data) {
            const cleanedRow = {};
            let hasValidData = true;

            // Map headers to standard names and clean values
            for (const [key, value] of Object.entries(row)) {
                let cleanKey = key.trim();
                let cleanValue = value;

                // Handle different decimal separators
                if (typeof cleanValue === 'string') {
                    cleanValue = cleanValue.replace(',', '.');
                    cleanValue = parseFloat(cleanValue);
                }

                // Skip rows with invalid numeric values
                if (isNaN(cleanValue) && this.isNumericColumn(cleanKey)) {
                    hasValidData = false;
                    break;
                }

                cleanedRow[cleanKey] = cleanValue;
            }

            if (hasValidData) {
                cleaned.push(cleanedRow);
            }
        }

        return cleaned;
    }

    isNumericColumn(columnName) {
        const numericColumns = [
            'UNIT CHARGE', 'REACTOR EFFLUENT FLOW', 'Temp', 'REACTOR FEED FLOW', 
            'Fouling', 'Resistance'
        ];
        return numericColumns.some(col => columnName.includes(col));
    }

    extractFeatures(data) {
        const X = [];
        const y = [];
        const dates = [];

        for (const row of data) {
            const featureRow = [];
            const headers = Object.keys(row);

            // Extract features - try exact match first, then partial match
            for (const feature of this.features) {
                let header = headers.find(h => h === feature);
                if (!header) {
                    header = headers.find(h =>
                        h.includes(feature.replace(/[()_]/g, '')) ||
                        feature.replace(/[()_]/g, '').includes(h.replace(/[()_]/g, ''))
                    );
                }
                if (header) {
                    featureRow.push(row[header]);
                } else {
                    throw new Error(`Feature ${feature} not found in data. Available headers: ${headers.join(', ')}`);
                }
            }

            // Extract target - exact match
            const targetHeader = headers.find(h => h === this.target);
            if (targetHeader) {
                y.push([row[targetHeader]]);
            } else {
                throw new Error(`Target column '${this.target}' not found. Available headers: ${headers.join(', ')}`);
            }

            // Extract date if available
            const dateHeader = headers.find(h =>
                h.toUpperCase().includes('DATE') || h.toLowerCase().includes('date')
            );
            if (dateHeader) {
                dates.push(row[dateHeader]);
            }

            X.push(featureRow);
        }

        return { X, y, dates };
    }

    normalizeFeatures(X) {
        if (!this.scaler) {
            this.scaler = this.fitScaler(X);
        }
        return this.transformWithScaler(X, this.scaler);
    }

    fitScaler(X) {
        const numFeatures = X[0].length;
        const means = [];
        const stds = [];

        for (let j = 0; j < numFeatures; j++) {
            const column = X.map(row => row[j]);
            const mean = column.reduce((sum, val) => sum + val, 0) / column.length;
            const variance = column.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / column.length;
            const std = Math.sqrt(variance);

            means.push(mean);
            stds.push(std === 0 ? 1 : std); // Avoid division by zero
        }

        return { means, stds };
    }

    transformWithScaler(X, scaler) {
        return X.map(row => 
            row.map((val, j) => (val - scaler.means[j]) / scaler.stds[j])
        );
    }

    inverseTransformTarget(y) {
        // For now, we don't normalize the target, so just return as is
        return y;
    }

    processDateFeatures(dates) {
        return dates.map(dateStr => {
            if (!dateStr) return [0, 0, 0]; // Default values

            let date;
            if (typeof dateStr === 'string') {
                // Try to parse different date formats
                const formats = [
                    /(\d{1,2})-(\d{1,2})-(\d{4})/, // DD-MM-YYYY
                    /(\d{4})-(\d{1,2})-(\d{1,2})/, // YYYY-MM-DD
                    /(\d{1,2})\/(\d{1,2})\/(\d{4})/ // DD/MM/YYYY
                ];

                for (const format of formats) {
                    const match = dateStr.match(format);
                    if (match) {
                        if (format === formats[1]) { // YYYY-MM-DD
                            date = new Date(match[1], match[2] - 1, match[3]);
                        } else { // DD-MM-YYYY or DD/MM/YYYY
                            date = new Date(match[3], match[2] - 1, match[1]);
                        }
                        break;
                    }
                }
            }

            if (!date || isNaN(date.getTime())) {
                return [0, 0, 0]; // Default values for invalid dates
            }

            const dayOfYear = Math.floor((date - new Date(date.getFullYear(), 0, 0)) / 86400000);
            const month = date.getMonth() + 1;
            const dayOfWeek = date.getDay();

            return [dayOfYear / 365, month / 12, dayOfWeek / 7]; // Normalized values
        });
    }

    filterByUnitCharge(data, minCharge, maxCharge) {
        return data.filter(row => {
            const unitCharge = row[0]; // Assuming unit charge is the first feature
            return unitCharge >= minCharge && unitCharge <= maxCharge;
        });
    }

    splitData(X, y, trainRatio = 0.8, valRatio = 0.1) {
        const totalSamples = X.length;
        const trainSize = Math.floor(totalSamples * trainRatio);
        const valSize = Math.floor(totalSamples * valRatio);

        // Shuffle indices
        const indices = Array.from({ length: totalSamples }, (_, i) => i);
        for (let i = indices.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [indices[i], indices[j]] = [indices[j], indices[i]];
        }

        const trainIndices = indices.slice(0, trainSize);
        const valIndices = indices.slice(trainSize, trainSize + valSize);
        const testIndices = indices.slice(trainSize + valSize);

        return {
            train: {
                X: trainIndices.map(i => X[i]),
                y: trainIndices.map(i => y[i])
            },
            validation: {
                X: valIndices.map(i => X[i]),
                y: valIndices.map(i => y[i])
            },
            test: {
                X: testIndices.map(i => X[i]),
                y: testIndices.map(i => y[i])
            }
        };
    }

    processData(csvText) {
        try {
            // Parse CSV
            const rawData = this.parseCSV(csvText);
            
            // Validate data structure
            this.validateData(rawData);
            
            // Clean data
            const cleanedData = this.cleanData(rawData);
            
            // Extract features and targets
            const { X, y, dates } = this.extractFeatures(cleanedData);
            
            // Process date features and add to X
            const dateFeatures = this.processDateFeatures(dates);
            const XWithDates = X.map((row, i) => [...row, ...dateFeatures[i]]);
            
            // Normalize features
            const XNormalized = this.normalizeFeatures(XWithDates);
            
            // Store processed data
            this.processedData = {
                X: XNormalized,
                y: y,
                rawData: XWithDates, // Include raw features before normalization
                dates: dates,
                originalData: cleanedData
            };

            return this.processedData;
            
        } catch (error) {
            throw new Error(`Data processing failed: ${error.message}`);
        }
    }

    getDataStats() {
        if (!this.processedData) {
            return null;
        }

        const { X, y } = this.processedData;
        
        return {
            samples: X.length,
            features: X[0].length,
            unitChargeRange: {
                min: Math.min(...X.map(row => row[0])),
                max: Math.max(...X.map(row => row[0]))
            },
            foulingResistanceRange: {
                min: Math.min(...y.map(row => row[0])),
                max: Math.max(...y.map(row => row[0]))
            }
        };
    }

    getPreviewData(maxRows = 10) {
        if (!this.processedData) {
            return null;
        }

        const { originalData } = this.processedData;
        return originalData.slice(0, maxRows);
    }
}
