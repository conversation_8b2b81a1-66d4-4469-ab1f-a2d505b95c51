// Advanced Analytics Module for Heat Exchanger Fouling Prediction
// Provides insights, diagnostics, and optimization recommendations

class AdvancedAnalytics {
    constructor(multiModelManager, dataProcessor) {
        this.multiModelManager = multiModelManager;
        this.dataProcessor = dataProcessor;
        this.insights = [];
        this.recommendations = [];
    }

    // Analyze model performance and generate insights
    analyzeModelPerformance() {
        const status = this.multiModelManager.getModelStatus();
        const insights = [];
        const recommendations = [];
        
        // Find best performing model
        let bestModel = null;
        let bestR2 = -1;
        
        Object.keys(status).forEach(interval => {
            const performance = status[interval].performance;
            if (performance.r2 && performance.r2 > bestR2) {
                bestR2 = performance.r2;
                bestModel = interval;
            }
        });
        
        if (bestModel) {
            insights.push({
                icon: '🏆',
                text: `Best performing model: ${bestModel}% unit charge interval with R² = ${bestR2.toFixed(3)}`
            });
            
            if (bestR2 > 0.9) {
                recommendations.push({
                    priority: 'low',
                    text: 'Excellent model performance. Continue with current operational parameters.'
                });
            } else if (bestR2 > 0.7) {
                recommendations.push({
                    priority: 'medium',
                    text: 'Good model performance. Consider collecting more data for improvement.'
                });
            } else {
                recommendations.push({
                    priority: 'high',
                    text: 'Model performance needs improvement. Review data quality and model parameters.'
                });
            }
        }
        
        // Analyze data distribution
        const totalData = Object.values(status).reduce((sum, s) => sum + s.dataCount, 0);
        const dataDistribution = Object.keys(status).map(interval => ({
            interval,
            count: status[interval].dataCount,
            percentage: (status[interval].dataCount / totalData * 100).toFixed(1)
        }));
        
        // Find intervals with insufficient data
        const insufficientData = dataDistribution.filter(d => d.count < 50);
        if (insufficientData.length > 0) {
            insights.push({
                icon: '⚠️',
                text: `Low data availability for: ${insufficientData.map(d => d.interval + '%').join(', ')}`
            });
            
            recommendations.push({
                priority: 'medium',
                text: 'Collect more operational data for underrepresented unit charge intervals.'
            });
        }
        
        // Analyze prediction consistency
        const trainedModels = Object.keys(status).filter(interval => 
            status[interval].performance.status === 'trained'
        );
        
        if (trainedModels.length >= 3) {
            insights.push({
                icon: '✅',
                text: `${trainedModels.length} models successfully trained and ready for predictions`
            });
        } else {
            insights.push({
                icon: '🔄',
                text: `Only ${trainedModels.length} models trained. Train more models for better coverage.`
            });
        }
        
        this.insights = insights;
        this.recommendations = recommendations;
        
        return { insights, recommendations };
    }

    // Calculate feature correlations
    calculateFeatureCorrelations(data) {
        if (!data || !data.X || data.X.length === 0) return null;
        
        const features = [
            'Unit Charge', 'Shell Flow', 'Shell Temp In', 'Shell Temp Out',
            'Tube Flow', 'Tube Temp In', 'Tube Temp Out'
        ];
        
        const correlations = {};
        
        // Calculate correlation with fouling resistance
        for (let i = 0; i < 7; i++) { // Only first 7 features (exclude date features)
            const featureValues = data.X.map(row => row[i]);
            const targetValues = data.y.map(row => row[0]);
            
            const correlation = this.calculatePearsonCorrelation(featureValues, targetValues);
            correlations[features[i]] = correlation;
        }
        
        return correlations;
    }

    // Calculate Pearson correlation coefficient
    calculatePearsonCorrelation(x, y) {
        const n = x.length;
        if (n === 0) return 0;
        
        const sumX = x.reduce((a, b) => a + b, 0);
        const sumY = y.reduce((a, b) => a + b, 0);
        const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
        const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0);
        const sumY2 = y.reduce((sum, yi) => sum + yi * yi, 0);
        
        const numerator = n * sumXY - sumX * sumY;
        const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
        
        return denominator === 0 ? 0 : numerator / denominator;
    }

    // Generate operational insights based on data patterns
    generateOperationalInsights(data) {
        if (!data || !data.X || data.X.length === 0) return [];
        
        const insights = [];
        
        // Analyze unit charge patterns
        const unitCharges = data.X.map(row => row[0]);
        const avgUnitCharge = unitCharges.reduce((a, b) => a + b, 0) / unitCharges.length;
        
        if (avgUnitCharge > 80) {
            insights.push({
                icon: '🔥',
                text: `High average unit charge (${avgUnitCharge.toFixed(1)}%) may accelerate fouling`
            });
        }
        
        // Analyze temperature differentials
        const shellTempDiffs = data.X.map(row => row[2] - row[3]); // Shell temp in - out
        const tubeTempDiffs = data.X.map(row => row[6] - row[5]); // Tube temp out - in
        
        const avgShellDiff = shellTempDiffs.reduce((a, b) => a + b, 0) / shellTempDiffs.length;
        const avgTubeDiff = tubeTempDiffs.reduce((a, b) => a + b, 0) / tubeTempDiffs.length;
        
        if (avgShellDiff > 150) {
            insights.push({
                icon: '🌡️',
                text: `High shell-side temperature differential (${avgShellDiff.toFixed(1)}°C) detected`
            });
        }
        
        // Analyze flow rate patterns
        const shellFlows = data.X.map(row => row[1]);
        const tubeFlows = data.X.map(row => row[4]);
        
        const flowRatios = shellFlows.map((sf, i) => sf / tubeFlows[i]);
        const avgFlowRatio = flowRatios.reduce((a, b) => a + b, 0) / flowRatios.length;
        
        if (Math.abs(avgFlowRatio - 1) > 0.1) {
            insights.push({
                icon: '💧',
                text: `Flow imbalance detected (ratio: ${avgFlowRatio.toFixed(2)}). Consider flow optimization.`
            });
        }
        
        return insights;
    }

    // Calculate system-wide metrics
    calculateSystemMetrics() {
        const status = this.multiModelManager.getModelStatus();
        const trainedModels = Object.keys(status).filter(interval => 
            status[interval].performance.status === 'trained'
        );
        
        if (trainedModels.length === 0) {
            return {
                bestModel: '--',
                systemAccuracy: '--',
                predictionReliability: '--',
                dataQuality: '--'
            };
        }
        
        // Calculate weighted average R²
        let totalR2 = 0;
        let totalWeight = 0;
        
        trainedModels.forEach(interval => {
            const performance = status[interval].performance;
            const weight = status[interval].dataCount;
            
            if (performance.r2) {
                totalR2 += performance.r2 * weight;
                totalWeight += weight;
            }
        });
        
        const systemAccuracy = totalWeight > 0 ? (totalR2 / totalWeight) : 0;
        
        // Calculate prediction reliability based on model consistency
        const r2Values = trainedModels.map(interval => status[interval].performance.r2).filter(r2 => r2);
        const r2StdDev = this.calculateStandardDeviation(r2Values);
        const predictionReliability = Math.max(0, 1 - r2StdDev);
        
        // Calculate data quality score
        const totalData = Object.values(status).reduce((sum, s) => sum + s.dataCount, 0);
        const balanceScore = this.calculateDataBalanceScore(status);
        const coverageScore = trainedModels.length / 5; // 5 total intervals
        const dataQuality = (balanceScore + coverageScore) / 2;
        
        // Find best model
        const bestModel = trainedModels.reduce((best, current) => {
            const currentR2 = status[current].performance.r2 || 0;
            const bestR2 = status[best].performance.r2 || 0;
            return currentR2 > bestR2 ? current : best;
        }, trainedModels[0]);
        
        return {
            bestModel: bestModel ? `${bestModel}%` : '--',
            systemAccuracy: (systemAccuracy * 100).toFixed(1) + '%',
            predictionReliability: (predictionReliability * 100).toFixed(1) + '%',
            dataQuality: (dataQuality * 100).toFixed(1) + '%'
        };
    }

    // Calculate standard deviation
    calculateStandardDeviation(values) {
        if (values.length === 0) return 0;
        
        const mean = values.reduce((a, b) => a + b, 0) / values.length;
        const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
        const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
        
        return Math.sqrt(avgSquaredDiff);
    }

    // Calculate data balance score
    calculateDataBalanceScore(status) {
        const counts = Object.values(status).map(s => s.dataCount);
        const totalCount = counts.reduce((a, b) => a + b, 0);
        
        if (totalCount === 0) return 0;
        
        const expectedCount = totalCount / counts.length;
        const deviations = counts.map(count => Math.abs(count - expectedCount) / expectedCount);
        const avgDeviation = deviations.reduce((a, b) => a + b, 0) / deviations.length;
        
        return Math.max(0, 1 - avgDeviation);
    }

    // Generate maintenance recommendations based on predictions
    generateMaintenanceRecommendations(predictions) {
        const recommendations = [];
        
        if (!predictions || Object.keys(predictions).length === 0) {
            recommendations.push({
                priority: 'low',
                text: 'Generate predictions to receive maintenance recommendations.'
            });
            return recommendations;
        }
        
        // Analyze prediction trends
        const allForecasts = Object.values(predictions).flat();
        const maxFouling = Math.max(...allForecasts.map(f => f.foulingResistance));
        const minFouling = Math.min(...allForecasts.map(f => f.foulingResistance));
        const avgFouling = allForecasts.reduce((sum, f) => sum + f.foulingResistance, 0) / allForecasts.length;
        
        // Critical thresholds
        const criticalThreshold = 0.007;
        const warningThreshold = 0.006;
        
        if (maxFouling > criticalThreshold) {
            recommendations.push({
                priority: 'high',
                text: `Critical fouling level predicted (${maxFouling.toFixed(6)} m² °C/W). Schedule immediate maintenance.`
            });
        } else if (maxFouling > warningThreshold) {
            recommendations.push({
                priority: 'medium',
                text: `Elevated fouling levels expected. Plan maintenance within 7 days.`
            });
        } else {
            recommendations.push({
                priority: 'low',
                text: `Fouling levels within normal range. Continue regular monitoring.`
            });
        }
        
        // Analyze trend
        const firstWeek = allForecasts.slice(0, 7);
        const secondWeek = allForecasts.slice(7, 14);
        
        if (firstWeek.length > 0 && secondWeek.length > 0) {
            const firstWeekAvg = firstWeek.reduce((sum, f) => sum + f.foulingResistance, 0) / firstWeek.length;
            const secondWeekAvg = secondWeek.reduce((sum, f) => sum + f.foulingResistance, 0) / secondWeek.length;
            
            const trendRate = (secondWeekAvg - firstWeekAvg) / firstWeekAvg * 100;
            
            if (trendRate > 5) {
                recommendations.push({
                    priority: 'medium',
                    text: `Accelerating fouling trend detected (+${trendRate.toFixed(1)}%). Monitor closely.`
                });
            }
        }
        
        return recommendations;
    }

    // Update all analytics displays
    updateAnalyticsDisplays(data = null, predictions = null) {
        // Update system metrics
        const metrics = this.calculateSystemMetrics();
        document.getElementById('best-model').textContent = metrics.bestModel;
        document.getElementById('system-accuracy').textContent = metrics.systemAccuracy;
        document.getElementById('prediction-reliability').textContent = metrics.predictionReliability;
        document.getElementById('data-quality').textContent = metrics.dataQuality;
        
        // Update insights
        const performanceAnalysis = this.analyzeModelPerformance();
        let allInsights = [...performanceAnalysis.insights];
        
        if (data) {
            const operationalInsights = this.generateOperationalInsights(data);
            allInsights = [...allInsights, ...operationalInsights];
        }
        
        this.updateInsightsDisplay(allInsights);
        
        // Update recommendations
        let allRecommendations = [...performanceAnalysis.recommendations];
        
        if (predictions) {
            const maintenanceRecs = this.generateMaintenanceRecommendations(predictions);
            allRecommendations = [...allRecommendations, ...maintenanceRecs];
        }
        
        this.updateRecommendationsDisplay(allRecommendations);
    }

    // Update insights display
    updateInsightsDisplay(insights) {
        const container = document.getElementById('operational-insights');
        
        if (insights.length === 0) {
            container.innerHTML = `
                <div class="insight-item">
                    <span class="insight-icon">💡</span>
                    <span class="insight-text">No insights available yet.</span>
                </div>
            `;
            return;
        }
        
        container.innerHTML = insights.map(insight => `
            <div class="insight-item">
                <span class="insight-icon">${insight.icon}</span>
                <span class="insight-text">${insight.text}</span>
            </div>
        `).join('');
    }

    // Update recommendations display
    updateRecommendationsDisplay(recommendations) {
        const container = document.getElementById('maintenance-recommendations');
        
        if (recommendations.length === 0) {
            container.innerHTML = `
                <div class="recommendation-item">
                    <span class="recommendation-priority low">Low</span>
                    <span class="recommendation-text">No recommendations available.</span>
                </div>
            `;
            return;
        }
        
        container.innerHTML = recommendations.map(rec => `
            <div class="recommendation-item">
                <span class="recommendation-priority ${rec.priority}">${rec.priority.charAt(0).toUpperCase() + rec.priority.slice(1)}</span>
                <span class="recommendation-text">${rec.text}</span>
            </div>
        `).join('');
    }
}
