// Test data processing in Node.js environment
const fs = require('fs');

// Mock DOM and browser APIs for Node.js testing
global.document = {
    createElement: () => ({ href: '', download: '', click: () => {} })
};
global.window = { URL: { createObjectURL: () => '', revokeObjectURL: () => {} } };
global.localStorage = { 
    setItem: () => {}, 
    getItem: () => null 
};

// Mock Papa Parse
global.Papa = {
    parse: (csvText, options) => {
        const lines = csvText.trim().split('\n');
        const headers = lines[0].split(',');
        const data = lines.slice(1).map(line => {
            const values = line.split(',');
            const row = {};
            headers.forEach((header, index) => {
                row[header.trim()] = values[index] ? values[index].trim() : '';
            });
            return row;
        });
        
        if (options.complete) {
            options.complete({ data });
        }
        
        return { data };
    }
};

// Load the JavaScript modules
eval(fs.readFileSync('neural-network.js', 'utf8'));
eval(fs.readFileSync('data-processor.js', 'utf8'));
eval(fs.readFileSync('multi-model-manager.js', 'utf8'));
eval(fs.readFileSync('advanced-analytics.js', 'utf8'));

async function testDataProcessing() {
    try {
        console.log('🔄 Starting data processing test...');
        
        // Load CSV data
        const csvData = fs.readFileSync('data.csv', 'utf8');
        console.log('✅ CSV data loaded');
        console.log(`📊 CSV size: ${csvData.length} characters`);
        
        // Create data processor
        const dataProcessor = new DataProcessor();
        console.log('✅ DataProcessor created');
        
        // Process the data
        const processedData = dataProcessor.processData(csvData);
        console.log('✅ Data processed successfully');
        console.log(`📊 Processed ${processedData.X.length} samples with ${processedData.X[0].length} features`);
        
        // Check if rawData is included
        if (processedData.rawData) {
            console.log('✅ Raw data included in processed output');
            console.log(`📊 Raw data: ${processedData.rawData.length} samples with ${processedData.rawData[0].length} features`);
        } else {
            console.log('❌ Raw data missing from processed output');
        }
        
        // Create multi-model manager
        const multiModelManager = new MultiModelManager();
        console.log('✅ MultiModelManager created');
        
        // Test data preparation
        multiModelManager.prepareTrainingData(processedData);
        console.log('✅ Multi-model data preparation completed');
        
        // Get model status
        const status = multiModelManager.getModelStatus();
        console.log('✅ Model status retrieved');
        
        // Display data distribution
        console.log('\n📈 Data Distribution by Unit Charge Interval:');
        Object.keys(status).forEach(interval => {
            const count = status[interval].dataCount;
            console.log(`   ${interval}%: ${count} samples`);
        });
        
        // Test analytics
        const advancedAnalytics = new AdvancedAnalytics(multiModelManager, dataProcessor);
        console.log('✅ AdvancedAnalytics created');
        
        // Test system metrics calculation
        const metrics = advancedAnalytics.calculateSystemMetrics();
        console.log('✅ System metrics calculated');
        console.log(`📊 Best model: ${metrics.bestModel}`);
        console.log(`📊 System accuracy: ${metrics.systemAccuracy}`);
        console.log(`📊 Data quality: ${metrics.dataQuality}`);
        
        // Test feature correlations
        const correlations = advancedAnalytics.calculateFeatureCorrelations(processedData);
        if (correlations) {
            console.log('✅ Feature correlations calculated');
            console.log('📊 Feature correlations with fouling:');
            Object.entries(correlations).forEach(([feature, corr]) => {
                console.log(`   ${feature}: ${corr.toFixed(3)}`);
            });
        }
        
        console.log('\n🎉 All tests passed! The data upload should work correctly in the browser.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('🔍 Stack trace:', error.stack);
    }
}

// Run the test
testDataProcessing();
