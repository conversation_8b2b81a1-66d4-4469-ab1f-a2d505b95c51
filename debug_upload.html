<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        .info { background: #e3f2fd; color: #1565c0; }
    </style>
</head>
<body>
    <h1>Debug Upload Test</h1>
    <input type="file" id="file-input" accept=".csv">
    <button onclick="testUpload()">Test Upload</button>
    <div id="log-container"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>
    <script src="neural-network.js"></script>
    <script src="data-processor.js"></script>
    <script src="multi-model-manager.js"></script>
    <script src="advanced-analytics.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const container = document.getElementById('log-container');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
            console.log(message);
        }
        
        // Override console.error to capture errors
        const originalError = console.error;
        console.error = function(...args) {
            log(`ERROR: ${args.join(' ')}`, 'error');
            originalError.apply(console, args);
        };
        
        // Capture unhandled errors
        window.addEventListener('error', function(e) {
            log(`UNHANDLED ERROR: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
        });
        
        async function testUpload() {
            try {
                log('🔄 Starting upload test...', 'info');
                
                // Create instances
                log('Creating DataProcessor...', 'info');
                const dataProcessor = new DataProcessor();
                log('✅ DataProcessor created', 'success');
                
                log('Creating MultiModelManager...', 'info');
                const multiModelManager = new MultiModelManager();
                log('✅ MultiModelManager created', 'success');
                
                log('Creating AdvancedAnalytics...', 'info');
                const advancedAnalytics = new AdvancedAnalytics(multiModelManager, dataProcessor);
                log('✅ AdvancedAnalytics created', 'success');
                
                // Load CSV file
                log('Loading data.csv...', 'info');
                const response = await fetch('data.csv');
                const csvText = await response.text();
                log(`✅ CSV loaded: ${csvText.length} characters`, 'success');
                
                // Process data
                log('Processing CSV data...', 'info');
                const processedData = dataProcessor.processData(csvText);
                log(`✅ Data processed: ${processedData.X.length} samples, ${processedData.X[0].length} features`, 'success');
                
                // Check for rawData
                if (processedData.rawData) {
                    log(`✅ Raw data available: ${processedData.rawData.length} samples`, 'success');
                } else {
                    log('❌ Raw data missing!', 'error');
                }
                
                // Test multi-model preparation
                log('Preparing multi-model data...', 'info');
                multiModelManager.prepareTrainingData(processedData);
                log('✅ Multi-model data prepared', 'success');
                
                // Get status
                log('Getting model status...', 'info');
                const status = multiModelManager.getModelStatus();
                log('✅ Model status retrieved', 'success');
                
                // Log data distribution
                Object.keys(status).forEach(interval => {
                    const count = status[interval].dataCount;
                    const perfStatus = status[interval].performance ? status[interval].performance.status : 'undefined';
                    log(`📊 ${interval}%: ${count} samples, performance: ${perfStatus}`, 'info');
                });
                
                // Test analytics
                log('Testing analytics...', 'info');
                advancedAnalytics.updateAnalyticsDisplays(processedData);
                log('✅ Analytics updated successfully', 'success');
                
                log('🎉 All tests completed successfully!', 'success');
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
            }
        }
        
        // Auto-load CSV file if available
        window.addEventListener('load', async function() {
            log('Page loaded, checking for data.csv...', 'info');
            try {
                const response = await fetch('data.csv');
                if (response.ok) {
                    log('✅ data.csv found, ready for testing', 'success');
                } else {
                    log('❌ data.csv not found', 'error');
                }
            } catch (error) {
                log(`❌ Error checking data.csv: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
