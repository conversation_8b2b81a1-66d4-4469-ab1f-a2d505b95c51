// Neural Network Implementation in JavaScript
// Adapted from the Python implementation

class Matrix {
    static dot(a, b) {
        const result = [];
        for (let i = 0; i < a.length; i++) {
            result[i] = [];
            for (let j = 0; j < b[0].length; j++) {
                let sum = 0;
                for (let k = 0; k < b.length; k++) {
                    sum += a[i][k] * b[k][j];
                }
                result[i][j] = sum;
            }
        }
        return result;
    }

    static transpose(matrix) {
        return matrix[0].map((_, colIndex) => matrix.map(row => row[colIndex]));
    }

    static add(a, b) {
        return a.map((row, i) => row.map((val, j) => val + b[i][j]));
    }

    static subtract(a, b) {
        return a.map((row, i) => row.map((val, j) => val - b[i][j]));
    }

    static multiply(a, b) {
        return a.map((row, i) => row.map((val, j) => val * b[i][j]));
    }

    static sum(matrix, axis = null) {
        if (axis === 0) {
            return matrix[0].map((_, j) => matrix.reduce((sum, row) => sum + row[j], 0));
        } else if (axis === 1) {
            return matrix.map(row => row.reduce((sum, val) => sum + val, 0));
        } else {
            return matrix.flat().reduce((sum, val) => sum + val, 0);
        }
    }

    static mean(matrix, axis = null) {
        if (axis === 0) {
            const sums = Matrix.sum(matrix, 0);
            return sums.map(sum => sum / matrix.length);
        } else if (axis === 1) {
            const sums = Matrix.sum(matrix, 1);
            return sums.map(sum => sum / matrix[0].length);
        } else {
            return Matrix.sum(matrix) / (matrix.length * matrix[0].length);
        }
    }

    static maximum(a, b) {
        if (typeof b === 'number') {
            return a.map(row => row.map(val => Math.max(val, b)));
        }
        return a.map((row, i) => row.map((val, j) => Math.max(val, b[i][j])));
    }

    static sqrt(matrix) {
        return matrix.map(row => row.map(val => Math.sqrt(val)));
    }

    static pow(matrix, power) {
        return matrix.map(row => row.map(val => Math.pow(val, power)));
    }

    static exp(matrix) {
        return matrix.map(row => row.map(val => Math.exp(val)));
    }

    static log(matrix) {
        return matrix.map(row => row.map(val => Math.log(val)));
    }

    static clip(matrix, min, max) {
        return matrix.map(row => row.map(val => Math.max(min, Math.min(max, val))));
    }

    static zeros(rows, cols) {
        return Array(rows).fill().map(() => Array(cols).fill(0));
    }

    static ones(rows, cols) {
        return Array(rows).fill().map(() => Array(cols).fill(1));
    }

    static random(rows, cols, scale = 0.01) {
        return Array(rows).fill().map(() => 
            Array(cols).fill().map(() => (Math.random() - 0.5) * 2 * scale)
        );
    }
}

// Dense Layer
class LayerDense {
    constructor(nInputs, nNeurons, weightRegularizerL1 = 0, weightRegularizerL2 = 0, 
                biasRegularizerL1 = 0, biasRegularizerL2 = 0) {
        this.weights = Matrix.random(nInputs, nNeurons, 0.01);
        this.biases = Matrix.zeros(1, nNeurons);
        this.weightRegularizerL1 = weightRegularizerL1;
        this.weightRegularizerL2 = weightRegularizerL2;
        this.biasRegularizerL1 = biasRegularizerL1;
        this.biasRegularizerL2 = biasRegularizerL2;
    }

    forward(inputs, training) {
        this.inputs = inputs;
        this.output = Matrix.add(Matrix.dot(inputs, this.weights), this.biases);
    }

    backward(dvalues) {
        this.dweights = Matrix.dot(Matrix.transpose(this.inputs), dvalues);
        this.dbiases = [Matrix.sum(dvalues, 0)];

        // L2 regularization on weights
        if (this.weightRegularizerL2 > 0) {
            const l2Penalty = Matrix.multiply(this.weights, 
                Array(this.weights.length).fill().map(() => 
                    Array(this.weights[0].length).fill(2 * this.weightRegularizerL2)
                )
            );
            this.dweights = Matrix.add(this.dweights, l2Penalty);
        }

        this.dinputs = Matrix.dot(dvalues, Matrix.transpose(this.weights));
    }

    getParameters() {
        return { weights: this.weights, biases: this.biases };
    }

    setParameters(weights, biases) {
        this.weights = weights;
        this.biases = biases;
    }
}

// Dropout Layer
class LayerDropout {
    constructor(rate) {
        this.rate = 1 - rate;
    }

    forward(inputs, training) {
        this.inputs = inputs;
        if (!training) {
            this.output = inputs.map(row => [...row]);
            return;
        }

        this.binaryMask = inputs.map(row => 
            row.map(() => Math.random() < this.rate ? 1 / this.rate : 0)
        );
        this.output = Matrix.multiply(inputs, this.binaryMask);
    }

    backward(dvalues) {
        this.dinputs = Matrix.multiply(dvalues, this.binaryMask);
    }
}

// ReLU Activation
class ActivationReLU {
    forward(inputs, training) {
        this.inputs = inputs;
        this.output = Matrix.maximum(inputs, 0);
    }

    backward(dvalues) {
        this.dinputs = dvalues.map((row, i) => 
            row.map((val, j) => this.inputs[i][j] <= 0 ? 0 : val)
        );
    }

    predictions(outputs) {
        return outputs;
    }
}

// Sigmoid Activation
class ActivationSigmoid {
    forward(inputs, training) {
        this.inputs = inputs;
        this.output = inputs.map(row => 
            row.map(val => 1 / (1 + Math.exp(-val)))
        );
    }

    backward(dvalues) {
        this.dinputs = Matrix.multiply(dvalues, 
            Matrix.multiply(
                Matrix.subtract(Matrix.ones(this.output.length, this.output[0].length), this.output),
                this.output
            )
        );
    }

    predictions(outputs) {
        return outputs.map(row => row.map(val => val > 0.5 ? 1 : 0));
    }
}

// Linear Activation
class ActivationLinear {
    forward(inputs, training) {
        this.inputs = inputs;
        this.output = inputs.map(row => [...row]);
    }

    backward(dvalues) {
        this.dinputs = dvalues.map(row => [...row]);
    }

    predictions(outputs) {
        return outputs;
    }
}

// Mean Squared Error Loss
class LossMeanSquaredError {
    constructor() {
        this.accumulatedSum = 0;
        this.accumulatedCount = 0;
    }

    forward(yPred, yTrue) {
        const sampleLosses = yPred.map((row, i) => 
            Matrix.mean([Matrix.pow(Matrix.subtract([row], [yTrue[i]]), 2)])
        );
        return sampleLosses;
    }

    backward(dvalues, yTrue) {
        const samples = dvalues.length;
        const outputs = dvalues[0].length;
        
        this.dinputs = Matrix.multiply(
            Matrix.subtract(yTrue, dvalues),
            Array(samples).fill().map(() => Array(outputs).fill(-2 / outputs))
        );
        
        this.dinputs = this.dinputs.map(row => 
            row.map(val => val / samples)
        );
    }

    calculate(output, y) {
        const sampleLosses = this.forward(output, y);
        const dataLoss = Matrix.mean([sampleLosses]);
        
        this.accumulatedSum += Matrix.sum([sampleLosses]);
        this.accumulatedCount += sampleLosses.length;
        
        return dataLoss;
    }

    calculateAccumulated() {
        return this.accumulatedSum / this.accumulatedCount;
    }

    newPass() {
        this.accumulatedSum = 0;
        this.accumulatedCount = 0;
    }
}

// Adam Optimizer
class OptimizerAdam {
    constructor(learningRate = 0.001, decay = 0, epsilon = 1e-7, beta1 = 0.9, beta2 = 0.999) {
        this.learningRate = learningRate;
        this.currentLearningRate = learningRate;
        this.decay = decay;
        this.iterations = 0;
        this.epsilon = epsilon;
        this.beta1 = beta1;
        this.beta2 = beta2;
    }

    preUpdateParams() {
        if (this.decay) {
            this.currentLearningRate = this.learningRate * (1 / (1 + this.decay * this.iterations));
        }
    }

    updateParams(layer) {
        if (!layer.weightMomentums) {
            layer.weightMomentums = Matrix.zeros(layer.weights.length, layer.weights[0].length);
            layer.weightCache = Matrix.zeros(layer.weights.length, layer.weights[0].length);
            layer.biasMomentums = Matrix.zeros(layer.biases.length, layer.biases[0].length);
            layer.biasCache = Matrix.zeros(layer.biases.length, layer.biases[0].length);
        }

        // Update momentum
        layer.weightMomentums = Matrix.add(
            Matrix.multiply(layer.weightMomentums, 
                Array(layer.weights.length).fill().map(() => Array(layer.weights[0].length).fill(this.beta1))
            ),
            Matrix.multiply(layer.dweights,
                Array(layer.weights.length).fill().map(() => Array(layer.weights[0].length).fill(1 - this.beta1))
            )
        );

        layer.biasMomentums = Matrix.add(
            Matrix.multiply(layer.biasMomentums,
                Array(layer.biases.length).fill().map(() => Array(layer.biases[0].length).fill(this.beta1))
            ),
            Matrix.multiply(layer.dbiases,
                Array(layer.biases.length).fill().map(() => Array(layer.biases[0].length).fill(1 - this.beta1))
            )
        );

        // Corrected momentum
        const weightMomentumsCorrected = Matrix.multiply(layer.weightMomentums,
            Array(layer.weights.length).fill().map(() => 
                Array(layer.weights[0].length).fill(1 / (1 - Math.pow(this.beta1, this.iterations + 1)))
            )
        );

        const biasMomentumsCorrected = Matrix.multiply(layer.biasMomentums,
            Array(layer.biases.length).fill().map(() => 
                Array(layer.biases[0].length).fill(1 / (1 - Math.pow(this.beta1, this.iterations + 1)))
            )
        );

        // Update cache
        layer.weightCache = Matrix.add(
            Matrix.multiply(layer.weightCache,
                Array(layer.weights.length).fill().map(() => Array(layer.weights[0].length).fill(this.beta2))
            ),
            Matrix.multiply(Matrix.pow(layer.dweights, 2),
                Array(layer.weights.length).fill().map(() => Array(layer.weights[0].length).fill(1 - this.beta2))
            )
        );

        layer.biasCache = Matrix.add(
            Matrix.multiply(layer.biasCache,
                Array(layer.biases.length).fill().map(() => Array(layer.biases[0].length).fill(this.beta2))
            ),
            Matrix.multiply(Matrix.pow(layer.dbiases, 2),
                Array(layer.biases.length).fill().map(() => Array(layer.biases[0].length).fill(1 - this.beta2))
            )
        );

        // Corrected cache
        const weightCacheCorrected = Matrix.multiply(layer.weightCache,
            Array(layer.weights.length).fill().map(() => 
                Array(layer.weights[0].length).fill(1 / (1 - Math.pow(this.beta2, this.iterations + 1)))
            )
        );

        const biasCacheCorrected = Matrix.multiply(layer.biasCache,
            Array(layer.biases.length).fill().map(() => 
                Array(layer.biases[0].length).fill(1 / (1 - Math.pow(this.beta2, this.iterations + 1)))
            )
        );

        // Update parameters
        const weightUpdate = Matrix.multiply(weightMomentumsCorrected,
            Matrix.add(Matrix.sqrt(weightCacheCorrected),
                Array(layer.weights.length).fill().map(() => Array(layer.weights[0].length).fill(this.epsilon))
            ).map(row => row.map(val => -this.currentLearningRate / val))
        );

        const biasUpdate = Matrix.multiply(biasMomentumsCorrected,
            Matrix.add(Matrix.sqrt(biasCacheCorrected),
                Array(layer.biases.length).fill().map(() => Array(layer.biases[0].length).fill(this.epsilon))
            ).map(row => row.map(val => -this.currentLearningRate / val))
        );

        layer.weights = Matrix.add(layer.weights, weightUpdate);
        layer.biases = Matrix.add(layer.biases, biasUpdate);
    }

    postUpdateParams() {
        this.iterations++;
    }
}

// Accuracy for Regression
class AccuracyRegression {
    constructor() {
        this.precision = null;
        this.accumulatedSum = 0;
        this.accumulatedCount = 0;
    }

    init(y) {
        const yFlat = y.flat();
        const mean = yFlat.reduce((sum, val) => sum + val, 0) / yFlat.length;
        const variance = yFlat.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / yFlat.length;
        this.precision = Math.sqrt(variance) / 250;
    }

    calculate(predictions, y) {
        const comparisons = predictions.map((row, i) =>
            row.map((pred, j) => Math.abs(pred - y[i][j]) < this.precision ? 1 : 0)
        );

        const accuracy = Matrix.mean(comparisons.flat());

        this.accumulatedSum += Matrix.sum(comparisons);
        this.accumulatedCount += comparisons.length * comparisons[0].length;

        return accuracy;
    }

    calculateAccumulated() {
        return this.accumulatedSum / this.accumulatedCount;
    }

    newPass() {
        this.accumulatedSum = 0;
        this.accumulatedCount = 0;
    }
}

// Model Class
class Model {
    constructor() {
        this.layers = [];
        this.loss = null;
        this.optimizer = null;
        this.accuracy = null;
        this.trainableLayers = [];
        this.inputLayer = null;
        this.outputLayerActivation = null;
    }

    add(layer) {
        this.layers.push(layer);
    }

    set(options = {}) {
        if (options.loss) this.loss = options.loss;
        if (options.optimizer) this.optimizer = options.optimizer;
        if (options.accuracy) this.accuracy = options.accuracy;
    }

    finalize() {
        this.inputLayer = { output: null };

        // Set up layer connections and find trainable layers
        for (let i = 0; i < this.layers.length; i++) {
            if (i === 0) {
                this.layers[i].prev = this.inputLayer;
                this.layers[i].next = this.layers[i + 1] || this.loss;
            } else if (i < this.layers.length - 1) {
                this.layers[i].prev = this.layers[i - 1];
                this.layers[i].next = this.layers[i + 1];
            } else {
                this.layers[i].prev = this.layers[i - 1];
                this.layers[i].next = this.loss;
                this.outputLayerActivation = this.layers[i];
            }

            if (this.layers[i].weights) {
                this.trainableLayers.push(this.layers[i]);
            }
        }
    }

    forward(X, training) {
        this.inputLayer.output = X;

        for (const layer of this.layers) {
            layer.forward(layer.prev.output, training);
        }

        return this.layers[this.layers.length - 1].output;
    }

    backward(output, y) {
        this.loss.backward(output, y);

        for (let i = this.layers.length - 1; i >= 0; i--) {
            this.layers[i].backward(this.layers[i].next.dinputs || this.loss.dinputs);
        }
    }

    async train(X, y, options = {}) {
        const epochs = options.epochs || 100;
        const batchSize = options.batchSize || 32;
        const printEvery = options.printEvery || 10;
        const onProgress = options.onProgress || (() => {});

        if (this.accuracy) {
            this.accuracy.init(y);
        }

        const trainSteps = Math.ceil(X.length / batchSize);

        for (let epoch = 1; epoch <= epochs; epoch++) {
            this.loss.newPass();
            if (this.accuracy) this.accuracy.newPass();

            for (let step = 0; step < trainSteps; step++) {
                const startIdx = step * batchSize;
                const endIdx = Math.min(startIdx + batchSize, X.length);
                const batchX = X.slice(startIdx, endIdx);
                const batchY = y.slice(startIdx, endIdx);

                const output = this.forward(batchX, true);
                const dataLoss = this.loss.calculate(output, batchY);

                let accuracy = null;
                if (this.accuracy) {
                    const predictions = this.outputLayerActivation.predictions(output);
                    accuracy = this.accuracy.calculate(predictions, batchY);
                }

                this.backward(output, batchY);

                this.optimizer.preUpdateParams();
                for (const layer of this.trainableLayers) {
                    this.optimizer.updateParams(layer);
                }
                this.optimizer.postUpdateParams();

                // Allow UI updates
                if (step % 5 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 0));
                }
            }

            const epochLoss = this.loss.calculateAccumulated();
            const epochAccuracy = this.accuracy ? this.accuracy.calculateAccumulated() : null;

            onProgress({
                epoch,
                epochs,
                loss: epochLoss,
                accuracy: epochAccuracy,
                learningRate: this.optimizer.currentLearningRate
            });

            if (epoch % printEvery === 0) {
                console.log(`Epoch ${epoch}/${epochs}, Loss: ${epochLoss.toFixed(6)}, Accuracy: ${epochAccuracy ? epochAccuracy.toFixed(3) : 'N/A'}`);
            }

            // Allow UI updates
            await new Promise(resolve => setTimeout(resolve, 0));
        }
    }

    predict(X) {
        const output = this.forward(X, false);
        return this.outputLayerActivation.predictions(output);
    }

    getParameters() {
        return this.trainableLayers.map(layer => layer.getParameters());
    }

    setParameters(parameters) {
        parameters.forEach((params, i) => {
            this.trainableLayers[i].setParameters(params.weights, params.biases);
        });
    }
}
