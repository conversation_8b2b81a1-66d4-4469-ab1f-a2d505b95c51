// Main Application Logic for Heat Exchanger Fouling Prediction

class FoulingPredictionApp {
    constructor() {
        this.dataProcessor = new DataProcessor();
        this.model = null;
        this.isTraining = false;
        this.trainingData = null;
        this.charts = {};
        
        this.initializeEventListeners();
        this.initializeCharts();
    }

    initializeEventListeners() {
        // File upload
        const fileInput = document.getElementById('csv-file');
        const uploadLabel = document.querySelector('.upload-label');
        
        fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        
        // Drag and drop
        uploadLabel.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadLabel.classList.add('drag-over');
        });
        
        uploadLabel.addEventListener('dragleave', () => {
            uploadLabel.classList.remove('drag-over');
        });
        
        uploadLabel.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadLabel.classList.remove('drag-over');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.processFile(files[0]);
            }
        });

        // Training controls
        document.getElementById('start-training').addEventListener('click', () => this.startTraining());
        document.getElementById('stop-training').addEventListener('click', () => this.stopTraining());
        document.getElementById('reset-model').addEventListener('click', () => this.resetModel());
        
        // Prediction
        document.getElementById('generate-prediction').addEventListener('click', () => this.generatePrediction());
    }

    initializeCharts() {
        // Loss chart
        const lossCtx = document.getElementById('loss-chart').getContext('2d');
        this.charts.loss = new Chart(lossCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Training Loss',
                    data: [],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Training Loss'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Accuracy chart
        const accuracyCtx = document.getElementById('accuracy-chart').getContext('2d');
        this.charts.accuracy = new Chart(accuracyCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Training Accuracy',
                    data: [],
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Training Accuracy'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1
                    }
                }
            }
        });

        // Prediction chart
        const predictionCtx = document.getElementById('prediction-chart').getContext('2d');
        this.charts.prediction = new Chart(predictionCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Fouling Resistance Prediction',
                    data: [],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Fouling Resistance Prediction'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Fouling Resistance (m² °C/W)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    }
                }
            }
        });
    }

    async handleFileUpload(event) {
        const files = event.target.files;
        if (files.length > 0) {
            await this.processFile(files[0]);
        }
    }

    async processFile(file) {
        const statusDiv = document.getElementById('upload-status');
        
        try {
            statusDiv.style.display = 'block';
            statusDiv.className = 'upload-status';
            statusDiv.innerHTML = '<div class="loading"></div> Processing file...';

            const text = await this.readFileAsText(file);
            const processedData = this.dataProcessor.processData(text);
            
            this.trainingData = processedData;
            this.displayDataPreview();
            this.enableTraining();
            
            statusDiv.className = 'upload-status success';
            statusDiv.innerHTML = `✓ File processed successfully! ${processedData.X.length} samples loaded.`;
            
        } catch (error) {
            statusDiv.className = 'upload-status error';
            statusDiv.innerHTML = `✗ Error: ${error.message}`;
            console.error('File processing error:', error);
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    displayDataPreview() {
        const previewDiv = document.getElementById('data-preview');
        const statsDiv = document.getElementById('data-stats');
        const headerDiv = document.getElementById('preview-header');
        const bodyDiv = document.getElementById('preview-body');
        
        const stats = this.dataProcessor.getDataStats();
        const previewData = this.dataProcessor.getPreviewData(10);
        
        // Display statistics
        statsDiv.innerHTML = `
            <div class="stat-item">
                <div class="stat-value">${stats.samples}</div>
                <div class="stat-label">Samples</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${stats.features}</div>
                <div class="stat-label">Features</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${stats.foulingResistanceRange.min.toFixed(6)}</div>
                <div class="stat-label">Min Fouling</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${stats.foulingResistanceRange.max.toFixed(6)}</div>
                <div class="stat-label">Max Fouling</div>
            </div>
        `;
        
        // Display preview table
        if (previewData && previewData.length > 0) {
            const headers = Object.keys(previewData[0]);
            
            headerDiv.innerHTML = headers.map(header => `<th>${header}</th>`).join('');
            bodyDiv.innerHTML = previewData.map(row => 
                `<tr>${headers.map(header => `<td>${this.formatValue(row[header])}</td>`).join('')}</tr>`
            ).join('');
        }
        
        previewDiv.style.display = 'block';
    }

    formatValue(value) {
        if (typeof value === 'number') {
            return value.toFixed(4);
        }
        return value || '';
    }

    enableTraining() {
        document.getElementById('start-training').disabled = false;
        document.getElementById('generate-prediction').disabled = false;
    }

    createModel() {
        const hiddenLayers = parseInt(document.getElementById('hidden-layers').value);
        const neuronsPerLayer = document.getElementById('neurons-per-layer').value.split(',').map(n => parseInt(n.trim()));
        const activation = document.getElementById('activation').value;
        const optimizer = document.getElementById('optimizer').value;
        const learningRate = parseFloat(document.getElementById('learning-rate').value);
        const l2Regularization = parseFloat(document.getElementById('l2-regularization').value);
        const dropoutRate = parseFloat(document.getElementById('dropout-rate').value);
        const lossFunction = document.getElementById('loss-function').value;

        this.model = new Model();
        
        // Input features (7 original + 3 date features)
        const inputSize = 10;
        
        // Add layers based on configuration
        for (let i = 0; i < hiddenLayers; i++) {
            const neurons = neuronsPerLayer[i] || neuronsPerLayer[neuronsPerLayer.length - 1];
            const prevSize = i === 0 ? inputSize : (neuronsPerLayer[i-1] || neuronsPerLayer[neuronsPerLayer.length - 1]);
            
            this.model.add(new LayerDense(prevSize, neurons, 0, l2Regularization));
            
            // Add activation
            if (activation === 'relu') {
                this.model.add(new ActivationReLU());
            } else if (activation === 'sigmoid') {
                this.model.add(new ActivationSigmoid());
            } else {
                this.model.add(new ActivationLinear());
            }
            
            // Add dropout
            if (dropoutRate > 0) {
                this.model.add(new LayerDropout(dropoutRate));
            }
        }
        
        // Output layer
        const lastHiddenSize = neuronsPerLayer[hiddenLayers - 1] || neuronsPerLayer[neuronsPerLayer.length - 1];
        this.model.add(new LayerDense(lastHiddenSize, 1)); // Single output for fouling resistance
        this.model.add(new ActivationLinear()); // Linear activation for regression
        
        // Set loss, optimizer, and accuracy
        const loss = new LossMeanSquaredError();
        
        let optimizerInstance;
        if (optimizer === 'adam') {
            optimizerInstance = new OptimizerAdam(learningRate);
        } else {
            optimizerInstance = new OptimizerAdam(learningRate); // Default to Adam for now
        }
        
        const accuracy = new AccuracyRegression();
        
        this.model.set({ loss, optimizer: optimizerInstance, accuracy });
        this.model.finalize();
        
        return this.model;
    }

    async startTraining() {
        if (!this.trainingData || this.isTraining) return;
        
        this.isTraining = true;
        document.getElementById('start-training').disabled = true;
        document.getElementById('stop-training').disabled = false;
        
        try {
            // Create model
            this.createModel();
            
            // Get training parameters
            const epochs = parseInt(document.getElementById('epochs').value);
            const batchSize = parseInt(document.getElementById('batch-size').value);
            
            // Split data
            const splitData = this.dataProcessor.splitData(this.trainingData.X, this.trainingData.y, 0.8, 0.1);
            
            // Clear charts
            this.clearCharts();
            
            // Start training
            await this.model.train(splitData.train.X, splitData.train.y, {
                epochs,
                batchSize,
                printEvery: 5,
                onProgress: (progress) => this.updateTrainingProgress(progress)
            });
            
            this.isTraining = false;
            document.getElementById('start-training').disabled = false;
            document.getElementById('stop-training').disabled = true;
            
            console.log('Training completed!');
            
        } catch (error) {
            console.error('Training error:', error);
            this.isTraining = false;
            document.getElementById('start-training').disabled = false;
            document.getElementById('stop-training').disabled = true;
        }
    }

    stopTraining() {
        this.isTraining = false;
        document.getElementById('start-training').disabled = false;
        document.getElementById('stop-training').disabled = true;
    }

    resetModel() {
        this.model = null;
        this.clearCharts();
        this.updateTrainingProgress({ epoch: 0, epochs: 0, loss: 0, accuracy: 0 });
    }

    clearCharts() {
        Object.values(this.charts).forEach(chart => {
            chart.data.labels = [];
            chart.data.datasets.forEach(dataset => {
                dataset.data = [];
            });
            chart.update();
        });
    }

    updateTrainingProgress(progress) {
        // Update progress display
        document.getElementById('current-epoch').textContent = `Epoch: ${progress.epoch}/${progress.epochs}`;
        document.getElementById('training-loss').textContent = `Loss: ${progress.loss.toFixed(6)}`;
        document.getElementById('training-accuracy').textContent = `Accuracy: ${progress.accuracy ? progress.accuracy.toFixed(3) : 'N/A'}`;
        
        // Update progress bar
        const progressPercent = (progress.epoch / progress.epochs) * 100;
        document.getElementById('progress-fill').style.width = `${progressPercent}%`;
        
        // Update charts
        this.charts.loss.data.labels.push(progress.epoch);
        this.charts.loss.data.datasets[0].data.push(progress.loss);
        this.charts.loss.update('none');
        
        if (progress.accuracy !== null) {
            this.charts.accuracy.data.labels.push(progress.epoch);
            this.charts.accuracy.data.datasets[0].data.push(progress.accuracy);
            this.charts.accuracy.update('none');
        }
    }

    generatePrediction() {
        if (!this.model) {
            alert('Please train a model first!');
            return;
        }
        
        // Get input values
        const inputs = [
            parseFloat(document.getElementById('unit-charge').value),
            parseFloat(document.getElementById('shell-flow').value),
            parseFloat(document.getElementById('shell-temp-in').value),
            parseFloat(document.getElementById('shell-temp-out').value),
            parseFloat(document.getElementById('tube-flow').value),
            parseFloat(document.getElementById('tube-temp-in').value),
            parseFloat(document.getElementById('tube-temp-out').value),
            0.5, 0.5, 0.5 // Default date features
        ];
        
        // Normalize inputs using the same scaler
        const normalizedInputs = this.dataProcessor.transformWithScaler([inputs], this.dataProcessor.scaler);
        
        // Make prediction
        const prediction = this.model.predict(normalizedInputs);
        const foulingResistance = prediction[0][0];
        
        // Display result
        document.getElementById('fouling-resistance').textContent = `${foulingResistance.toFixed(8)} m² °C/W`;
        document.getElementById('prediction-results').style.display = 'block';
        
        // Update prediction chart with a simple visualization
        this.updatePredictionChart(foulingResistance);
    }

    updatePredictionChart(prediction) {
        const chart = this.charts.prediction;
        const now = new Date();
        const labels = [];
        const data = [];
        
        // Generate 14 days of predictions (simplified)
        for (let i = 0; i < 14; i++) {
            const date = new Date(now);
            date.setDate(date.getDate() + i);
            labels.push(date.toLocaleDateString());
            // Simple trend simulation
            data.push(prediction * (1 + (Math.random() - 0.5) * 0.1));
        }
        
        chart.data.labels = labels;
        chart.data.datasets[0].data = data;
        chart.update();
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new FoulingPredictionApp();
});
