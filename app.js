// Main Application Logic for Heat Exchanger Fouling Prediction

class FoulingPredictionApp {
    constructor() {
        this.dataProcessor = new DataProcessor();
        this.multiModelManager = new MultiModelManager();
        this.advancedAnalytics = new AdvancedAnalytics(this.multiModelManager, this.dataProcessor);
        this.isTraining = false;
        this.trainingData = null;
        this.charts = {};
        this.lastPredictions = null;

        this.initializeEventListeners();
        this.initializeCharts();
    }

    initializeEventListeners() {
        // File upload
        const fileInput = document.getElementById('csv-file');
        const uploadLabel = document.querySelector('.upload-label');
        
        fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        
        // Drag and drop
        uploadLabel.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadLabel.classList.add('drag-over');
        });
        
        uploadLabel.addEventListener('dragleave', () => {
            uploadLabel.classList.remove('drag-over');
        });
        
        uploadLabel.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadLabel.classList.remove('drag-over');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.processFile(files[0]);
            }
        });

        // Training controls
        document.getElementById('start-training').addEventListener('click', () => this.startTraining());
        document.getElementById('stop-training').addEventListener('click', () => this.stopTraining());
        document.getElementById('reset-models').addEventListener('click', () => this.resetModels());
        document.getElementById('save-models').addEventListener('click', () => this.saveModels());
        document.getElementById('load-models').addEventListener('click', () => this.loadModels());

        // Prediction
        document.getElementById('generate-prediction').addEventListener('click', () => this.generatePrediction());
        document.getElementById('export-predictions').addEventListener('click', () => this.exportPredictions());
    }

    initializeCharts() {
        // Multi-model loss chart
        const multiLossCtx = document.getElementById('multi-loss-chart').getContext('2d');
        this.charts.multiLoss = new Chart(multiLossCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    { label: '50-60%', data: [], borderColor: '#e74c3c', backgroundColor: 'rgba(231, 76, 60, 0.1)', tension: 0.4 },
                    { label: '60-70%', data: [], borderColor: '#f39c12', backgroundColor: 'rgba(243, 156, 18, 0.1)', tension: 0.4 },
                    { label: '70-80%', data: [], borderColor: '#2ecc71', backgroundColor: 'rgba(46, 204, 113, 0.1)', tension: 0.4 },
                    { label: '80-90%', data: [], borderColor: '#3498db', backgroundColor: 'rgba(52, 152, 219, 0.1)', tension: 0.4 },
                    { label: '90-100%', data: [], borderColor: '#9b59b6', backgroundColor: 'rgba(155, 89, 182, 0.1)', tension: 0.4 }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Multi-Model Training Loss' }
                },
                scales: { y: { beginAtZero: true } }
            }
        });

        // Multi-model accuracy chart
        const multiAccuracyCtx = document.getElementById('multi-accuracy-chart').getContext('2d');
        this.charts.multiAccuracy = new Chart(multiAccuracyCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    { label: '50-60%', data: [], borderColor: '#e74c3c', backgroundColor: 'rgba(231, 76, 60, 0.1)', tension: 0.4 },
                    { label: '60-70%', data: [], borderColor: '#f39c12', backgroundColor: 'rgba(243, 156, 18, 0.1)', tension: 0.4 },
                    { label: '70-80%', data: [], borderColor: '#2ecc71', backgroundColor: 'rgba(46, 204, 113, 0.1)', tension: 0.4 },
                    { label: '80-90%', data: [], borderColor: '#3498db', backgroundColor: 'rgba(52, 152, 219, 0.1)', tension: 0.4 },
                    { label: '90-100%', data: [], borderColor: '#9b59b6', backgroundColor: 'rgba(155, 89, 182, 0.1)', tension: 0.4 }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Multi-Model Training Accuracy' }
                },
                scales: { y: { beginAtZero: true, max: 1 } }
            }
        });

        // Forecast chart
        const forecastCtx = document.getElementById('forecast-chart').getContext('2d');
        this.charts.forecast = new Chart(forecastCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: '14-Day Fouling Resistance Forecast' }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: 'Fouling Resistance (m² °C/W)' }
                    },
                    x: {
                        title: { display: true, text: 'Date' }
                    }
                }
            }
        });

        // Confidence chart
        const confidenceCtx = document.getElementById('confidence-chart').getContext('2d');
        this.charts.confidence = new Chart(confidenceCtx, {
            type: 'bar',
            data: {
                labels: ['50-60%', '60-70%', '70-80%', '80-90%', '90-100%'],
                datasets: [{
                    label: 'Model Confidence',
                    data: [],
                    backgroundColor: ['#e74c3c', '#f39c12', '#2ecc71', '#3498db', '#9b59b6']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Model Prediction Confidence' }
                },
                scales: { y: { beginAtZero: true, max: 1 } }
            }
        });

        // Model comparison chart
        const comparisonCtx = document.getElementById('model-comparison-chart').getContext('2d');
        this.charts.comparison = new Chart(comparisonCtx, {
            type: 'radar',
            data: {
                labels: ['RMSE', 'MAE', 'R²', 'Data Size', 'Training Time'],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Model Performance Comparison' }
                },
                scales: {
                    r: { beginAtZero: true, max: 1 }
                }
            }
        });

        // Performance comparison chart
        const perfCompCtx = document.getElementById('performance-comparison-chart').getContext('2d');
        this.charts.performanceComparison = new Chart(perfCompCtx, {
            type: 'bar',
            data: {
                labels: ['50-60%', '60-70%', '70-80%', '80-90%', '90-100%'],
                datasets: [
                    { label: 'RMSE', data: [], backgroundColor: '#e74c3c' },
                    { label: 'MAE', data: [], backgroundColor: '#f39c12' },
                    { label: 'R²', data: [], backgroundColor: '#2ecc71' }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Performance Metrics Comparison' }
                },
                scales: { y: { beginAtZero: true } }
            }
        });

        // Data distribution chart
        const dataDistCtx = document.getElementById('data-distribution-chart').getContext('2d');
        this.charts.dataDistribution = new Chart(dataDistCtx, {
            type: 'doughnut',
            data: {
                labels: ['50-60%', '60-70%', '70-80%', '80-90%', '90-100%'],
                datasets: [{
                    data: [],
                    backgroundColor: ['#e74c3c', '#f39c12', '#2ecc71', '#3498db', '#9b59b6']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Data Distribution by Unit Charge' }
                }
            }
        });

        // Feature correlation chart
        try {
            const featureCorrelationCtx = document.getElementById('feature-correlation-chart');
            if (featureCorrelationCtx) {
                this.charts.featureCorrelation = new Chart(featureCorrelationCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: ['Unit Charge', 'Shell Flow', 'Shell Temp In', 'Shell Temp Out', 'Tube Flow', 'Tube Temp In', 'Tube Temp Out'],
                        datasets: [{
                            label: 'Correlation with Fouling',
                            data: [],
                            backgroundColor: ['#e74c3c', '#f39c12', '#2ecc71', '#3498db', '#9b59b6', '#e67e22', '#1abc9c']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: { display: true, text: 'Feature Correlation Analysis' }
                        },
                        scales: {
                            y: { min: -1, max: 1 }
                        }
                    }
                });
            }
        } catch (error) {
            console.warn('Failed to initialize feature correlation chart:', error);
        }

        // Residual analysis chart
        try {
            const residualCtx = document.getElementById('residual-analysis-chart');
            if (residualCtx) {
                this.charts.residualAnalysis = new Chart(residualCtx.getContext('2d'), {
                    type: 'scatter',
                    data: {
                        datasets: [{
                            label: 'Residuals',
                            data: [],
                            backgroundColor: '#3498db'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: { display: true, text: 'Residual Analysis' }
                        },
                        scales: {
                            x: { title: { display: true, text: 'Predicted Values' } },
                            y: { title: { display: true, text: 'Residuals' } }
                        }
                    }
                });
            }
        } catch (error) {
            console.warn('Failed to initialize residual analysis chart:', error);
        }

        // Trend analysis chart
        try {
            const trendCtx = document.getElementById('trend-analysis-chart');
            if (trendCtx) {
                this.charts.trendAnalysis = new Chart(trendCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: 'Historical Trend',
                            data: [],
                            borderColor: '#2ecc71',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: { display: true, text: 'Fouling Resistance Trend Analysis' }
                        },
                        scales: {
                            y: { title: { display: true, text: 'Fouling Resistance (m² °C/W)' } }
                        }
                    }
                });
            }
        } catch (error) {
            console.warn('Failed to initialize trend analysis chart:', error);
        }

        // Sensitivity analysis chart
        try {
            const sensitivityCtx = document.getElementById('sensitivity-analysis-chart');
            if (sensitivityCtx) {
                this.charts.sensitivityAnalysis = new Chart(sensitivityCtx.getContext('2d'), {
                    type: 'radar',
                    data: {
                        labels: ['Unit Charge', 'Shell Flow', 'Shell Temp', 'Tube Flow', 'Tube Temp'],
                        datasets: [{
                            label: 'Sensitivity',
                            data: [],
                            borderColor: '#9b59b6',
                            backgroundColor: 'rgba(155, 89, 182, 0.2)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: { display: true, text: 'Parameter Sensitivity Analysis' }
                        },
                        scales: {
                            r: { beginAtZero: true, max: 1 }
                        }
                    }
                });
            }
        } catch (error) {
            console.warn('Failed to initialize sensitivity analysis chart:', error);
        }

        // Optimization chart
        try {
            const optimizationCtx = document.getElementById('optimization-chart');
            if (optimizationCtx) {
                this.charts.optimization = new Chart(optimizationCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [
                            {
                                label: 'Current Operation',
                                data: [],
                                borderColor: '#e74c3c',
                                backgroundColor: 'rgba(231, 76, 60, 0.1)'
                            },
                            {
                                label: 'Optimized Operation',
                                data: [],
                                borderColor: '#2ecc71',
                                backgroundColor: 'rgba(46, 204, 113, 0.1)'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: { display: true, text: 'Operational Optimization' }
                        },
                        scales: {
                            y: { title: { display: true, text: 'Fouling Resistance (m² °C/W)' } }
                        }
                    }
                });
            }
        } catch (error) {
            console.warn('Failed to initialize optimization chart:', error);
        }
    }

    async handleFileUpload(event) {
        const files = event.target.files;
        if (files.length > 0) {
            await this.processFile(files[0]);
        }
    }

    async processFile(file) {
        const statusDiv = document.getElementById('upload-status');
        
        try {
            statusDiv.style.display = 'block';
            statusDiv.className = 'upload-status';
            statusDiv.innerHTML = '<div class="loading"></div> Processing file...';

            const text = await this.readFileAsText(file);
            const processedData = this.dataProcessor.processData(text);
            
            this.trainingData = processedData;
            this.displayDataPreview();
            this.prepareMultiModelData();
            this.updateAdvancedAnalytics();
            this.enableTraining();

            statusDiv.className = 'upload-status success';
            statusDiv.innerHTML = `✓ File processed successfully! ${processedData.X.length} samples loaded.`;
            
        } catch (error) {
            statusDiv.className = 'upload-status error';
            statusDiv.innerHTML = `✗ Error: ${error.message}`;
            console.error('File processing error:', error);
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    displayDataPreview() {
        const previewDiv = document.getElementById('data-preview');
        const statsDiv = document.getElementById('data-stats');
        const headerDiv = document.getElementById('preview-header');
        const bodyDiv = document.getElementById('preview-body');
        
        const stats = this.dataProcessor.getDataStats();
        const previewData = this.dataProcessor.getPreviewData(10);
        
        // Display statistics
        statsDiv.innerHTML = `
            <div class="stat-item">
                <div class="stat-value">${stats.samples}</div>
                <div class="stat-label">Samples</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${stats.features}</div>
                <div class="stat-label">Features</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${stats.foulingResistanceRange.min.toFixed(6)}</div>
                <div class="stat-label">Min Fouling</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${stats.foulingResistanceRange.max.toFixed(6)}</div>
                <div class="stat-label">Max Fouling</div>
            </div>
        `;
        
        // Display preview table
        if (previewData && previewData.length > 0) {
            const headers = Object.keys(previewData[0]);
            
            headerDiv.innerHTML = headers.map(header => `<th>${header}</th>`).join('');
            bodyDiv.innerHTML = previewData.map(row => 
                `<tr>${headers.map(header => `<td>${this.formatValue(row[header])}</td>`).join('')}</tr>`
            ).join('');
        }
        
        previewDiv.style.display = 'block';
    }

    formatValue(value) {
        if (typeof value === 'number') {
            return value.toFixed(4);
        }
        return value || '';
    }

    prepareMultiModelData() {
        // Prepare data for multi-model training
        this.multiModelManager.prepareTrainingData(this.trainingData);

        // Update data distribution chart
        const status = this.multiModelManager.getModelStatus();
        const distributionData = Object.keys(status).map(interval => status[interval].dataCount);

        this.charts.dataDistribution.data.datasets[0].data = distributionData;
        this.charts.dataDistribution.update();

        // Update progress displays with data counts
        Object.keys(status).forEach(interval => {
            const progressItem = document.querySelector(`[data-interval="${interval}"]`);
            if (progressItem) {
                const dataCountSpan = progressItem.querySelector('.data-count');
                dataCountSpan.textContent = `Data: ${status[interval].dataCount}`;
            }
        });
    }

    enableTraining() {
        document.getElementById('start-training').disabled = false;
        document.getElementById('generate-prediction').disabled = false;
    }

    getModelConfig() {
        return {
            hiddenLayers: parseInt(document.getElementById('hidden-layers').value),
            neuronsPerLayer: document.getElementById('neurons-per-layer').value.split(',').map(n => parseInt(n.trim())),
            activation: document.getElementById('activation').value,
            optimizer: document.getElementById('optimizer').value,
            learningRate: parseFloat(document.getElementById('learning-rate').value),
            l2Regularization: parseFloat(document.getElementById('l2-regularization').value),
            dropoutRate: parseFloat(document.getElementById('dropout-rate').value),
            lossFunction: document.getElementById('loss-function').value,
            epochs: parseInt(document.getElementById('epochs').value),
            batchSize: parseInt(document.getElementById('batch-size').value)
        };
    }

    async startTraining() {
        if (!this.trainingData || this.isTraining) return;

        this.isTraining = true;
        document.getElementById('start-training').disabled = true;
        document.getElementById('stop-training').disabled = false;
        document.getElementById('save-models').disabled = true;

        try {
            // Get model configuration
            const config = this.getModelConfig();

            // Initialize models
            this.multiModelManager.initializeModels(config);

            // Clear charts
            this.clearCharts();

            // Start multi-model training
            await this.multiModelManager.trainAllModels(config, (intervalName, progress) => {
                this.updateModelProgress(intervalName, progress);
            });

            // Update performance displays
            this.updatePerformanceDisplays();
            this.updateAdvancedAnalytics();

            this.isTraining = false;
            document.getElementById('start-training').disabled = false;
            document.getElementById('stop-training').disabled = true;
            document.getElementById('save-models').disabled = false;

            console.log('Multi-model training completed!');

        } catch (error) {
            console.error('Training error:', error);
            this.isTraining = false;
            document.getElementById('start-training').disabled = false;
            document.getElementById('stop-training').disabled = true;
        }
    }

    stopTraining() {
        this.isTraining = false;
        this.multiModelManager.isTraining = false;
        document.getElementById('start-training').disabled = false;
        document.getElementById('stop-training').disabled = true;
    }

    resetModels() {
        this.multiModelManager = new MultiModelManager();
        this.clearCharts();
        this.resetProgressDisplays();
        this.resetPerformanceDisplays();
        document.getElementById('save-models').disabled = true;
    }

    saveModels() {
        const success = this.multiModelManager.saveModels();
        if (success) {
            alert('Models saved successfully!');
        } else {
            alert('Failed to save models. Please try again.');
        }
    }

    loadModels() {
        const config = this.getModelConfig();
        const success = this.multiModelManager.loadModels(config);
        if (success) {
            this.updatePerformanceDisplays();
            this.resetProgressDisplays();
            document.getElementById('save-models').disabled = false;
            alert('Models loaded successfully!');
        } else {
            alert('No saved models found or failed to load models.');
        }
    }

    clearCharts() {
        Object.values(this.charts).forEach(chart => {
            chart.data.labels = [];
            chart.data.datasets.forEach(dataset => {
                dataset.data = [];
            });
            chart.update();
        });
    }

    updateModelProgress(intervalName, progress) {
        // Update individual model progress display
        const progressItem = document.querySelector(`[data-interval="${intervalName}"]`);
        if (progressItem) {
            const epochSpan = progressItem.querySelector('.current-epoch');
            const lossSpan = progressItem.querySelector('.training-loss');
            const progressFill = progressItem.querySelector('.progress-fill');

            epochSpan.textContent = `Epoch: ${progress.epoch}/${progress.epochs}`;
            lossSpan.textContent = `Loss: ${progress.loss.toFixed(6)}`;

            const progressPercent = (progress.epoch / progress.epochs) * 100;
            progressFill.style.width = `${progressPercent}%`;

            // Update visual status
            progressItem.classList.remove('training', 'completed');
            if (progress.status === 'training') {
                progressItem.classList.add('training');
            } else if (progress.status === 'completed') {
                progressItem.classList.add('completed');
            }
        }

        // Update multi-model charts
        const intervalIndex = ['50-60', '60-70', '70-80', '80-90', '90-100'].indexOf(intervalName);
        if (intervalIndex !== -1) {
            // Ensure we have enough data points
            while (this.charts.multiLoss.data.labels.length < progress.epoch) {
                this.charts.multiLoss.data.labels.push(this.charts.multiLoss.data.labels.length + 1);
            }

            // Update loss data
            while (this.charts.multiLoss.data.datasets[intervalIndex].data.length < progress.epoch) {
                this.charts.multiLoss.data.datasets[intervalIndex].data.push(null);
            }
            this.charts.multiLoss.data.datasets[intervalIndex].data[progress.epoch - 1] = progress.loss;

            // Update accuracy data if available
            if (progress.accuracy !== null) {
                while (this.charts.multiAccuracy.data.datasets[intervalIndex].data.length < progress.epoch) {
                    this.charts.multiAccuracy.data.datasets[intervalIndex].data.push(null);
                }
                this.charts.multiAccuracy.data.datasets[intervalIndex].data[progress.epoch - 1] = progress.accuracy;
            }

            // Update charts periodically to avoid performance issues
            if (progress.epoch % 5 === 0 || progress.status === 'completed') {
                this.charts.multiLoss.update('none');
                this.charts.multiAccuracy.update('none');
            }
        }
    }

    resetProgressDisplays() {
        const progressItems = document.querySelectorAll('.model-progress-item');
        progressItems.forEach(item => {
            const epochSpan = item.querySelector('.current-epoch');
            const lossSpan = item.querySelector('.training-loss');
            const progressFill = item.querySelector('.progress-fill');

            epochSpan.textContent = 'Epoch: 0/0';
            lossSpan.textContent = 'Loss: --';
            progressFill.style.width = '0%';

            item.classList.remove('training', 'completed');
        });
    }

    updatePerformanceDisplays() {
        const status = this.multiModelManager.getModelStatus();

        // Update performance cards
        Object.keys(status).forEach(interval => {
            const card = document.querySelector(`.performance-card[data-interval="${interval}"]`);
            if (card) {
                const performance = status[interval].performance || {};

                const metricValues = card.querySelectorAll('.metric-value');
                if (metricValues.length >= 3) {
                    metricValues[0].textContent = performance.rmse ? performance.rmse.toFixed(6) : '--';
                    metricValues[1].textContent = performance.mae ? performance.mae.toFixed(6) : '--';
                    metricValues[2].textContent = performance.r2 ? performance.r2.toFixed(3) : '--';
                }

                const statusSpan = card.querySelector('.metric-status');
                if (statusSpan) {
                    const isTrained = performance.status === 'trained';
                    statusSpan.textContent = isTrained ? 'Trained' : 'Not Trained';
                    statusSpan.className = `metric-status ${isTrained ? 'trained' : 'not-trained'}`;

                    if (isTrained) {
                        card.classList.add('trained');
                    } else {
                        card.classList.remove('trained');
                    }
                }
            }
        });

        // Update performance comparison chart
        const intervals = ['50-60', '60-70', '70-80', '80-90', '90-100'];
        const rmseData = intervals.map(interval => (status[interval].performance && status[interval].performance.rmse) || 0);
        const maeData = intervals.map(interval => (status[interval].performance && status[interval].performance.mae) || 0);
        const r2Data = intervals.map(interval => (status[interval].performance && status[interval].performance.r2) || 0);

        this.charts.performanceComparison.data.datasets[0].data = rmseData;
        this.charts.performanceComparison.data.datasets[1].data = maeData;
        this.charts.performanceComparison.data.datasets[2].data = r2Data;
        this.charts.performanceComparison.update();
    }

    resetPerformanceDisplays() {
        const cards = document.querySelectorAll('.performance-card');
        cards.forEach(card => {
            card.querySelectorAll('.metric-value').forEach(span => span.textContent = '--');
            const statusSpan = card.querySelector('.metric-status');
            statusSpan.textContent = 'Not Trained';
            statusSpan.className = 'metric-status not-trained';
            card.classList.remove('trained');
        });
    }

    generatePrediction() {
        const trainedModels = Object.keys(this.multiModelManager.performance).filter(
            interval => this.multiModelManager.performance[interval] && this.multiModelManager.performance[interval].status === 'trained'
        );

        if (trainedModels.length === 0) {
            alert('Please train models first!');
            return;
        }

        // Get input values (raw values)
        const rawInputs = [
            parseFloat(document.getElementById('unit-charge').value),
            parseFloat(document.getElementById('shell-flow').value),
            parseFloat(document.getElementById('shell-temp-in').value),
            parseFloat(document.getElementById('shell-temp-out').value),
            parseFloat(document.getElementById('tube-flow').value),
            parseFloat(document.getElementById('tube-temp-in').value),
            parseFloat(document.getElementById('tube-temp-out').value),
            0.5, 0.5, 0.5 // Default date features
        ];

        // Normalize inputs using the same scaler
        const normalizedInputs = this.dataProcessor.transformWithScaler([rawInputs], this.dataProcessor.scaler);

        // Get forecast days
        const forecastDays = parseInt(document.getElementById('prediction-days').value);

        // Generate multi-model predictions - pass both raw and normalized inputs
        const predictions = this.multiModelManager.generatePredictions(normalizedInputs[0], forecastDays);

        // Store raw unit charge for display
        const unitCharge = rawInputs[0];

        // Store predictions for analytics
        this.lastPredictions = predictions;

        // Display results
        this.displayPredictionResults(predictions, unitCharge);

        // Update advanced analytics with predictions
        this.updateAdvancedAnalytics();

        document.getElementById('prediction-results').style.display = 'block';
        document.getElementById('export-predictions').disabled = false;
    }

    displayPredictionResults(predictions, unitCharge) {
        // Calculate summary statistics
        const allForecasts = Object.values(predictions).flat();
        if (allForecasts.length === 0) return;

        const currentFouling = allForecasts[0].foulingResistance;
        const avgFouling = allForecasts.reduce((sum, f) => sum + f.foulingResistance, 0) / allForecasts.length;

        // Determine trend
        const firstWeek = allForecasts.slice(0, 7).reduce((sum, f) => sum + f.foulingResistance, 0) / 7;
        const secondWeek = allForecasts.slice(7, 14).reduce((sum, f) => sum + f.foulingResistance, 0) / 7;
        const trend = secondWeek > firstWeek ? 'increasing' : secondWeek < firstWeek ? 'decreasing' : 'stable';

        // Determine maintenance alert
        const maxFouling = Math.max(...allForecasts.map(f => f.foulingResistance));
        const alertLevel = maxFouling > 0.007 ? 'high' : maxFouling > 0.006 ? 'medium' : 'low';
        const alertText = alertLevel === 'high' ? 'Immediate' : alertLevel === 'medium' ? 'Schedule Soon' : 'Normal';

        // Update summary display
        document.getElementById('current-fouling').textContent = `${currentFouling.toFixed(8)} m² °C/W`;
        document.getElementById('avg-fouling').textContent = `${avgFouling.toFixed(8)} m² °C/W`;

        const trendSpan = document.getElementById('fouling-trend');
        trendSpan.textContent = trend.charAt(0).toUpperCase() + trend.slice(1);
        trendSpan.className = `result-trend ${trend}`;

        const alertSpan = document.getElementById('maintenance-alert');
        alertSpan.textContent = alertText;
        alertSpan.className = `result-alert ${alertLevel}`;

        // Update forecast chart
        this.updateForecastChart(predictions);

        // Update confidence chart
        this.updateConfidenceChart(predictions);
    }

    updateForecastChart(predictions) {
        const chart = this.charts.forecast;
        const colors = ['#e74c3c', '#f39c12', '#2ecc71', '#3498db', '#9b59b6'];

        // Clear existing datasets
        chart.data.datasets = [];
        chart.data.labels = [];

        // Get all unique dates
        const allDates = new Set();
        Object.values(predictions).forEach(forecast => {
            forecast.forEach(point => allDates.add(point.date));
        });

        chart.data.labels = Array.from(allDates).sort();

        // Add dataset for each model
        let colorIndex = 0;
        Object.keys(predictions).forEach(interval => {
            const forecast = predictions[interval];
            const data = chart.data.labels.map(date => {
                const point = forecast.find(p => p.date === date);
                return point ? point.foulingResistance : null;
            });

            chart.data.datasets.push({
                label: `${interval}% Unit Charge`,
                data: data,
                borderColor: colors[colorIndex],
                backgroundColor: colors[colorIndex] + '20',
                tension: 0.4,
                fill: false
            });

            colorIndex++;
        });

        chart.update();
    }

    updateConfidenceChart(predictions) {
        const intervals = ['50-60', '60-70', '70-80', '80-90', '90-100'];
        const confidenceData = intervals.map(interval => {
            if (predictions[interval]) {
                // Calculate confidence based on data availability and model performance
                const performance = this.multiModelManager.performance[interval];
                const r2 = (performance && performance.r2) || 0;
                return Math.max(0, Math.min(1, r2));
            }
            return 0;
        });

        this.charts.confidence.data.datasets[0].data = confidenceData;
        this.charts.confidence.update();
    }

    exportPredictions() {
        // Use the last generated predictions if available
        if (!this.lastPredictions || Object.keys(this.lastPredictions).length === 0) {
            alert('Please generate predictions first!');
            return;
        }

        const predictions = this.lastPredictions;

        let csvContent = 'Date,Unit Charge Interval,Fouling Resistance (m² °C/W)\n';

        Object.keys(predictions).forEach(interval => {
            predictions[interval].forEach(point => {
                csvContent += `${point.date},${interval}%,${point.foulingResistance.toFixed(8)}\n`;
            });
        });

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'fouling_predictions.csv';
        a.click();
        window.URL.revokeObjectURL(url);
    }

    updateAdvancedAnalytics() {
        // Update analytics displays with current data and predictions
        this.advancedAnalytics.updateAnalyticsDisplays(this.trainingData, this.lastPredictions);

        // Update feature correlation chart if data is available and chart exists
        if (this.trainingData && this.charts.featureCorrelation) {
            try {
                const correlations = this.advancedAnalytics.calculateFeatureCorrelations(this.trainingData);
                if (correlations) {
                    const correlationValues = Object.values(correlations);
                    this.charts.featureCorrelation.data.datasets[0].data = correlationValues;
                    this.charts.featureCorrelation.update();
                }
            } catch (error) {
                console.warn('Failed to update feature correlation chart:', error);
            }
        }

        // Update trend analysis if historical data is available and chart exists
        if (this.trainingData && this.trainingData.y && this.charts.trendAnalysis) {
            try {
                const trendData = this.trainingData.y.slice(0, 100).map((value, index) => ({
                    x: index,
                    y: value[0]
                }));

                this.charts.trendAnalysis.data.labels = trendData.map((_, i) => i);
                this.charts.trendAnalysis.data.datasets[0].data = trendData.map(d => d.y);
                this.charts.trendAnalysis.update();
            } catch (error) {
                console.warn('Failed to update trend analysis chart:', error);
            }
        }

        // Update sensitivity analysis with mock data for demonstration
        if (this.trainingData && this.charts.sensitivityAnalysis) {
            try {
                const sensitivityData = [0.8, 0.6, 0.7, 0.5, 0.4]; // Mock sensitivity values
                this.charts.sensitivityAnalysis.data.datasets[0].data = sensitivityData;
                this.charts.sensitivityAnalysis.update();
            } catch (error) {
                console.warn('Failed to update sensitivity analysis chart:', error);
            }
        }
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new FoulingPredictionApp();
});
