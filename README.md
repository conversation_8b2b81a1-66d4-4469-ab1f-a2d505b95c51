# Heat Exchanger Fouling Prediction Web Application - Development Prompt

## Project Overview
Create a comprehensive web-based application for predicting fouling in heat exchangers using neural networks. The application should enable data-driven maintenance predictions for petroleum refinery operations, potentially saving billions of dollars through optimized maintenance scheduling.

## Technical Architecture
- **Frontend**: HTML, CSS, JavaScript (client-side solution)
- **Neural Network**: Use the provided custom neural network implementation (not TensorFlow/PyTorch)
- **Data Processing**: Client-side CSV parsing and preprocessing
- **Training**: Browser-based model training for 5 separate neural networks

## Data Structure Requirements

### Input CSV Format
```
UNIT CHARGE,REACTOR EFFLUENT FLOW,Temp(In)_Shell,Temp(Out)_Shell,REACTOR FEED FLOW,Temp(In)_Tube,Temp(Out)_Tube,DATE
%,kg/h,°C,°C,kg/h,°C,°C,YYYY-MM-DD,m² °C/W
```

### Features (Input Variables)
- Unit Charge (%)
- Shell Side: Mass flow (kg/h), Temperature In (°C), Temperature Out (°C)
- Tube Side: Mass flow (kg/h), Temperature In (°C), Temperature Out (°C)
- Date (daily intervals)

### Target Variable
- Fouling Resistance (m² °C/W)
- Heat Transfer Q(shell side)
- Heat Transfer Q(tube side)

## Core Functionality

### 1. Data Upload System
- **Multiple CSV Upload**: 5 separate file uploads for each unit charge interval
- **Unit Charge Intervals**: Fixed intervals (50-60%, 60-70%, 70-80%, 80-90%, 90-100%)
- **File Types**: Training, Validation, and Testing datasets for each interval
- **Data Validation**: Ensure proper CSV format and required columns

### 2. Model Configuration Interface
**Default Recommended Settings for Fouling Prediction:**
- **Architecture**: Dense layers (3-4 hidden layers with 64, 32, 16, 8 neurons)
- **Activation Functions**: ReLU for hidden layers, Linear for output (regression task)
- **Optimizer**: Adam (learning_rate=0.001, beta_1=0.9, beta_2=0.999)
- **Loss Function**: Mean Squared Error (MSE) for regression
- **Regularization**: L2 regularization (weight_regularizer_l2=0.01)
- **Training Parameters**: epochs=100, batch_size=32, validation split

**User Configurable Options:**
- Number of hidden layers (1-10)
- Neurons per layer (8, 16, 32, 64, 128, 256)
- Activation functions: ReLU, Sigmoid, Linear
- Optimizers: Adam, SGD, RMSprop, Adagrad
- Loss functions: MSE, MAE, Binary/Categorical Cross-entropy
- Learning rate (0.0001 - 0.1)
- Epochs (50-500)
- Batch size (16, 32, 64, 128)
- Regularization strength (0.0-0.1)
- Dropout rate (0.0-0.5)

### 3. Multi-Model Training System
- **5 Parallel Models**: One neural network for each unit charge interval
- **Data Filtering**: Automatically filter training data based on unit charge intervals
- **Independent Training**: Each model trains only on data from its specific interval
- **Progress Tracking**: Real-time training progress for all 5 models
- **Model Persistence**: Save/load trained models

### 4. Prediction Engine
- **14-Day Forecasting**: Daily fouling resistance predictions for next 2 weeks
- **Multi-Interval Prediction**: Generate predictions from all 5 models simultaneously
- **Real-time Input**: Accept current operational parameters for prediction
- **Uncertainty Quantification**: Display prediction confidence intervals

### 5. Visualization Dashboard
**Main Prediction Chart:**
- **X-axis**: Time (14 days ahead)
- **Y-axis**: Fouling Resistance (m² °C/W)
- **Multiple Lines**: 5 prediction curves (one per unit charge interval)
- **Actual vs Predicted**: Overlay actual values when available for comparison
- **Legend**: Clear identification of each unit charge interval
- **Interactive**: Hover tooltips with exact values and dates

**Additional Visualizations:**
- Training loss curves for each model
- Model performance metrics (RMSE, MAE, R²)
- Data distribution plots by unit charge interval
- Feature correlation heatmaps
- Prediction confidence bands

## Data Preprocessing Requirements
1. **Date Handling**: Convert date strings to numerical features (day of year, month, etc.)
2. **Unit Charge Filtering**: Separate data into 5 intervals automatically
3. **Feature Scaling**: Normalize input features (StandardScaler equivalent)
4. **Missing Value Handling**: Interpolation or removal strategies
5. **Outlier Detection**: Statistical outlier identification and handling
6. **Heat Transfer Calculation**: Compute Q = mc(Tout - Tin) from temperature and flow data

## User Interface Requirements

### Layout Structure
```
Header: "Heat Exchanger Fouling Prediction System"
├── Data Upload Section
│   ├── 5 File Upload Areas (one per unit charge interval)
│   └── Data Preview Tables
├── Model Configuration Panel
│   ├── Architecture Settings
│   ├── Training Parameters
│   └── Advanced Options (collapsible)
├── Training Dashboard
│   ├── Progress Bars (5 models)
│   ├── Real-time Loss Plots
│   └── Training Controls (Start/Stop/Reset)
├── Prediction Interface
│   ├── Current Parameters Input
│   ├── Generate Predictions Button
│   └── Export Results
└── Visualization Area
    ├── Main Prediction Chart
    ├── Model Performance Metrics
    └── Data Analysis Plots
```

### Styling Requirements
- **Professional Theme**: Clean, industrial design suitable for engineering applications
- **Responsive Design**: Works on desktop and tablet devices
- **Color Scheme**: Professional blues and grays with accent colors for different intervals
- **Typography**: Clear, technical font (Roboto or similar)
- **Interactive Elements**: Hover effects, smooth transitions
- **Status Indicators**: Clear visual feedback for training progress and data quality

## Technical Implementation Details

### Neural Network Integration
- Adapt the provided neural network classes for browser use
- Implement model serialization for saving/loading
- Add progress callbacks for training visualization
- Ensure memory efficiency for browser constraints

### Performance Considerations
- **Batch Processing**: Handle large datasets efficiently
- **Web Workers**: Use for heavy computations to prevent UI blocking
- **Memory Management**: Efficient data structures and cleanup
- **Caching**: Cache trained models and preprocessed data

### Error Handling
- **File Validation**: Check CSV format and required columns
- **Data Quality**: Validate numerical ranges and missing values
- **Training Errors**: Graceful handling of convergence issues
- **User Feedback**: Clear error messages and suggestions

## Specific Features for Fouling Prediction

### Domain-Specific Calculations
- Heat transfer coefficient degradation tracking
- Fouling resistance trend analysis
- Maintenance scheduling recommendations
- Critical fouling threshold alerts

### Industry Requirements
- **Units Display**: Show all values with proper engineering units
- **Precision**: Display results with appropriate decimal places
- **Export Options**: CSV/PDF reports for documentation
- **Compliance**: Maintain audit trail of predictions and model versions

## Success Criteria
1. **Accuracy**: Models achieve <10% MAPE on test data
2. **Usability**: Non-technical users can operate the interface
3. **Performance**: Training completes within 5 minutes per model
4. **Reliability**: Handles various data quality issues gracefully
5. **Scalability**: Supports datasets with 1000+ daily records per interval

## Implementation Priority
1. **Phase 1**: Basic UI, file upload, single model training
2. **Phase 2**: Multi-model system, basic predictions
3. **Phase 3**: Advanced visualizations, model optimization
4. **Phase 4**: Real-time predictions, export features, polish

Create a fully functional web application that enables petroleum engineers to upload heat exchanger data, train multiple neural networks for different operating conditions, and visualize fouling predictions to optimize maintenance scheduling.