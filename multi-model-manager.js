// Multi-Model Manager for Heat Exchanger Fouling Prediction
// Manages 5 separate neural networks for different unit charge intervals

class MultiModelManager {
    constructor() {
        this.models = {};
        this.intervals = [
            { name: '50-60', min: 50, max: 60 },
            { name: '60-70', min: 60, max: 70 },
            { name: '70-80', min: 70, max: 80 },
            { name: '80-90', min: 80, max: 90 },
            { name: '90-100', min: 90, max: 100 }
        ];
        this.trainingData = {};
        this.isTraining = false;
        this.trainingProgress = {};
        this.performance = {};
    }

    // Initialize models for all intervals
    initializeModels(config) {
        for (const interval of this.intervals) {
            this.models[interval.name] = this.createModel(config);
            this.trainingProgress[interval.name] = {
                epoch: 0,
                epochs: 0,
                loss: 0,
                accuracy: 0,
                dataCount: 0,
                status: 'not-trained'
            };
            this.performance[interval.name] = {
                rmse: null,
                mae: null,
                r2: null,
                status: 'not-trained'
            };
        }
    }

    // Create a single model with given configuration
    createModel(config) {
        const model = new Model();
        
        // Input features (7 original + 3 date features)
        const inputSize = 10;
        
        // Add layers based on configuration
        const hiddenLayers = config.hiddenLayers || 3;
        const neuronsPerLayer = config.neuronsPerLayer || [64, 32, 16];
        const activation = config.activation || 'relu';
        const l2Regularization = config.l2Regularization || 0.01;
        const dropoutRate = config.dropoutRate || 0.2;

        for (let i = 0; i < hiddenLayers; i++) {
            const neurons = neuronsPerLayer[i] || neuronsPerLayer[neuronsPerLayer.length - 1];
            const prevSize = i === 0 ? inputSize : (neuronsPerLayer[i-1] || neuronsPerLayer[neuronsPerLayer.length - 1]);
            
            model.add(new LayerDense(prevSize, neurons, 0, l2Regularization));
            
            // Add activation
            if (activation === 'relu') {
                model.add(new ActivationReLU());
            } else if (activation === 'sigmoid') {
                model.add(new ActivationSigmoid());
            } else {
                model.add(new ActivationLinear());
            }
            
            // Add dropout
            if (dropoutRate > 0) {
                model.add(new LayerDropout(dropoutRate));
            }
        }
        
        // Output layer
        const lastHiddenSize = neuronsPerLayer[hiddenLayers - 1] || neuronsPerLayer[neuronsPerLayer.length - 1];
        model.add(new LayerDense(lastHiddenSize, 1));
        model.add(new ActivationLinear());
        
        // Set loss, optimizer, and accuracy
        const loss = new LossMeanSquaredError();
        const optimizer = new OptimizerAdam(config.learningRate || 0.001);
        const accuracy = new AccuracyRegression();
        
        model.set({ loss, optimizer, accuracy });
        model.finalize();
        
        return model;
    }

    // Filter and prepare data for each interval
    prepareTrainingData(processedData) {
        const { X, y, rawData } = processedData;

        // Use raw data for filtering since X is normalized
        const rawX = rawData || X;

        // Ensure training data and progress objects are initialized
        if (!this.trainingData) {
            this.trainingData = {};
        }

        for (const interval of this.intervals) {
            const filteredIndices = [];

            // Filter data by unit charge interval using raw data
            for (let i = 0; i < rawX.length; i++) {
                const unitCharge = rawX[i][0]; // Unit charge is first feature in raw data
                if (unitCharge >= interval.min && unitCharge < interval.max) {
                    filteredIndices.push(i);
                }
            }

            // Extract filtered data using normalized X and y
            const intervalX = filteredIndices.map(i => X[i]);
            const intervalY = filteredIndices.map(i => y[i]);

            // Split data for this interval
            const splitData = this.splitData(intervalX, intervalY, 0.8, 0.1);

            this.trainingData[interval.name] = splitData;

            // Ensure progress tracking is initialized
            if (!this.trainingProgress[interval.name]) {
                this.trainingProgress[interval.name] = {
                    epoch: 0,
                    epochs: 0,
                    loss: 0,
                    accuracy: 0,
                    dataCount: 0,
                    status: 'not-trained'
                };
            }

            this.trainingProgress[interval.name].dataCount = intervalX.length;

            console.log(`Interval ${interval.name}: ${intervalX.length} samples`);
        }
    }

    // Split data into train/validation/test sets
    splitData(X, y, trainRatio = 0.8, valRatio = 0.1) {
        const totalSamples = X.length;
        const trainSize = Math.floor(totalSamples * trainRatio);
        const valSize = Math.floor(totalSamples * valRatio);

        // Shuffle indices
        const indices = Array.from({ length: totalSamples }, (_, i) => i);
        for (let i = indices.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [indices[i], indices[j]] = [indices[j], indices[i]];
        }

        const trainIndices = indices.slice(0, trainSize);
        const valIndices = indices.slice(trainSize, trainSize + valSize);
        const testIndices = indices.slice(trainSize + valSize);

        return {
            train: {
                X: trainIndices.map(i => X[i]),
                y: trainIndices.map(i => y[i])
            },
            validation: {
                X: valIndices.map(i => X[i]),
                y: valIndices.map(i => y[i])
            },
            test: {
                X: testIndices.map(i => X[i]),
                y: testIndices.map(i => y[i])
            }
        };
    }

    // Train all models simultaneously
    async trainAllModels(config, onProgress) {
        if (this.isTraining) return;
        
        this.isTraining = true;
        const epochs = config.epochs || 100;
        const batchSize = config.batchSize || 32;
        
        // Start training all models in parallel
        const trainingPromises = this.intervals.map(async (interval) => {
            const model = this.models[interval.name];
            const data = this.trainingData[interval.name];
            
            if (!data || data.train.X.length === 0) {
                console.warn(`No training data for interval ${interval.name}`);
                return;
            }
            
            this.trainingProgress[interval.name].status = 'training';
            this.trainingProgress[interval.name].epochs = epochs;
            
            try {
                await model.train(data.train.X, data.train.y, {
                    epochs,
                    batchSize,
                    printEvery: 5,
                    onProgress: (progress) => {
                        this.trainingProgress[interval.name] = {
                            ...this.trainingProgress[interval.name],
                            epoch: progress.epoch,
                            loss: progress.loss,
                            accuracy: progress.accuracy,
                            status: 'training'
                        };
                        
                        if (onProgress) {
                            onProgress(interval.name, this.trainingProgress[interval.name]);
                        }
                    }
                });
                
                // Calculate performance metrics
                await this.calculatePerformance(interval.name);
                
                this.trainingProgress[interval.name].status = 'completed';
                this.performance[interval.name].status = 'trained';
                
            } catch (error) {
                console.error(`Training failed for interval ${interval.name}:`, error);
                this.trainingProgress[interval.name].status = 'error';
            }
        });
        
        // Wait for all models to complete training
        await Promise.all(trainingPromises);
        
        this.isTraining = false;
        console.log('All models training completed');
    }

    // Calculate performance metrics for a model
    async calculatePerformance(intervalName) {
        const model = this.models[intervalName];
        const data = this.trainingData[intervalName];
        
        if (!data || !data.test || data.test.X.length === 0) {
            console.warn(`No test data for interval ${intervalName}`);
            return;
        }
        
        // Make predictions on test set
        const predictions = model.predict(data.test.X);
        const actual = data.test.y;
        
        // Calculate RMSE
        let sumSquaredError = 0;
        let sumAbsoluteError = 0;
        
        for (let i = 0; i < predictions.length; i++) {
            const error = predictions[i][0] - actual[i][0];
            sumSquaredError += error * error;
            sumAbsoluteError += Math.abs(error);
        }
        
        const rmse = Math.sqrt(sumSquaredError / predictions.length);
        const mae = sumAbsoluteError / predictions.length;
        
        // Calculate R²
        const actualMean = actual.reduce((sum, val) => sum + val[0], 0) / actual.length;
        let totalSumSquares = 0;
        
        for (let i = 0; i < actual.length; i++) {
            totalSumSquares += Math.pow(actual[i][0] - actualMean, 2);
        }
        
        const r2 = 1 - (sumSquaredError / totalSumSquares);
        
        this.performance[intervalName] = {
            rmse: rmse,
            mae: mae,
            r2: r2,
            status: 'trained'
        };
        
        console.log(`Performance for ${intervalName}: RMSE=${rmse.toFixed(6)}, MAE=${mae.toFixed(6)}, R²=${r2.toFixed(3)}`);
    }

    // Generate predictions for all applicable models
    generatePredictions(inputParameters, forecastDays = 14) {
        const predictions = {};

        // Get unit charge from raw input (before normalization)
        // We need to denormalize or use the original value
        let unitCharge;
        if (Array.isArray(inputParameters) && inputParameters.length > 0) {
            // If inputParameters is already normalized, we need the raw value
            // For now, let's try to find the best matching interval based on all available models
            unitCharge = inputParameters[0]; // This might be normalized, so we'll check all intervals
        }

        // If we can't determine unit charge, use all trained models
        let applicableIntervals = [];

        if (unitCharge !== undefined) {
            // Try to find matching intervals (this works if unitCharge is raw)
            applicableIntervals = this.intervals.filter(interval =>
                unitCharge >= interval.min && unitCharge < interval.max
            );
        }

        // If no intervals found or unitCharge seems normalized, use all trained models
        if (applicableIntervals.length === 0) {
            applicableIntervals = this.intervals.filter(interval =>
                this.models[interval.name] && this.performance[interval.name].status === 'trained'
            );
        }

        // Generate predictions for applicable intervals
        for (const interval of applicableIntervals) {
            const model = this.models[interval.name];
            if (model && this.performance[interval.name].status === 'trained') {
                const forecast = this.generateForecast(model, inputParameters, forecastDays);
                predictions[interval.name] = forecast;
            }
        }

        return predictions;
    }

    // Generate forecast for a specific model
    generateForecast(model, baseParameters, days) {
        const forecast = [];
        const now = new Date();
        
        for (let day = 0; day < days; day++) {
            const date = new Date(now);
            date.setDate(date.getDate() + day);
            
            // Create input with slight variations to simulate real conditions
            const dayInput = [...baseParameters];
            
            // Add small random variations to simulate operational changes
            for (let i = 1; i < 7; i++) { // Skip unit charge, vary other parameters
                dayInput[i] *= (1 + (Math.random() - 0.5) * 0.05); // ±2.5% variation
            }
            
            // Update date features
            const dayOfYear = Math.floor((date - new Date(date.getFullYear(), 0, 0)) / 86400000);
            const month = date.getMonth() + 1;
            const dayOfWeek = date.getDay();
            
            dayInput[7] = dayOfYear / 365;
            dayInput[8] = month / 12;
            dayInput[9] = dayOfWeek / 7;
            
            const prediction = model.predict([dayInput]);
            
            forecast.push({
                date: date.toISOString().split('T')[0],
                foulingResistance: prediction[0][0],
                day: day
            });
        }
        
        return forecast;
    }

    // Get model status summary
    getModelStatus() {
        const status = {};
        for (const interval of this.intervals) {
            status[interval.name] = {
                progress: this.trainingProgress[interval.name],
                performance: this.performance[interval.name],
                dataCount: this.trainingData[interval.name] ? this.trainingData[interval.name].train.X.length : 0
            };
        }
        return status;
    }

    // Save models to browser storage
    saveModels() {
        try {
            const modelData = {};
            for (const interval of this.intervals) {
                if (this.models[interval.name] && this.performance[interval.name].status === 'trained') {
                    modelData[interval.name] = {
                        parameters: this.models[interval.name].getParameters(),
                        performance: this.performance[interval.name]
                    };
                }
            }
            
            localStorage.setItem('foulingPredictionModels', JSON.stringify(modelData));
            console.log('Models saved successfully');
            return true;
        } catch (error) {
            console.error('Failed to save models:', error);
            return false;
        }
    }

    // Load models from browser storage
    loadModels(config) {
        try {
            const modelData = JSON.parse(localStorage.getItem('foulingPredictionModels'));
            if (!modelData) return false;
            
            // Initialize models first
            this.initializeModels(config);
            
            for (const interval of this.intervals) {
                if (modelData[interval.name]) {
                    this.models[interval.name].setParameters(modelData[interval.name].parameters);
                    this.performance[interval.name] = modelData[interval.name].performance;
                    this.trainingProgress[interval.name].status = 'completed';
                }
            }
            
            console.log('Models loaded successfully');
            return true;
        } catch (error) {
            console.error('Failed to load models:', error);
            return false;
        }
    }
}
