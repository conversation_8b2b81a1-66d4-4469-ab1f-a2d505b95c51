/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

/* Header */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem 0;
    text-align: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

header h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

header p {
    color: #7f8c8d;
    font-size: 1.1rem;
    font-weight: 300;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Sections */
.section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.section h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    border-bottom: 3px solid #3498db;
    padding-bottom: 0.5rem;
}

.section h3 {
    color: #34495e;
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

/* File Upload */
.upload-area {
    margin-bottom: 2rem;
}

.file-upload {
    position: relative;
    display: inline-block;
    width: 100%;
}

.upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 150px;
    border: 3px dashed #3498db;
    border-radius: 10px;
    background: rgba(52, 152, 219, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-label:hover {
    border-color: #2980b9;
    background: rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
}

.upload-icon {
    font-size: 3rem;
    margin-right: 1rem;
}

.upload-text {
    text-align: center;
}

.upload-text strong {
    display: block;
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.upload-text span {
    color: #7f8c8d;
    font-size: 0.9rem;
}

#csv-file {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.upload-status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    display: none;
}

.upload-status.success {
    background: rgba(46, 204, 113, 0.1);
    border: 1px solid #2ecc71;
    color: #27ae60;
}

.upload-status.error {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid #e74c3c;
    color: #c0392b;
}

/* Data Preview */
.data-preview {
    margin-top: 2rem;
}

.data-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(52, 152, 219, 0.1);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    min-width: 120px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2980b9;
}

.stat-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-top: 0.5rem;
}

.table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #ddd;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
}

tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* Configuration Grid */
.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.config-group {
    background: rgba(248, 249, 250, 0.8);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-right: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Training Controls */
.training-controls {
    margin-bottom: 2rem;
    text-align: center;
}

.training-progress {
    margin-bottom: 2rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.progress-info span {
    font-weight: 500;
    color: #2c3e50;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 0%;
    transition: width 0.3s ease;
}

/* Charts */
.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.chart-wrapper {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Prediction Interface */
.input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.prediction-results {
    margin-top: 2rem;
    padding: 2rem;
    background: rgba(46, 204, 113, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.result-value {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-label {
    font-size: 1.2rem;
    font-weight: 500;
    color: #2c3e50;
}

.result-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #27ae60;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .section {
        padding: 1.5rem;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-container {
        grid-template-columns: 1fr;
    }
    
    .input-grid {
        grid-template-columns: 1fr;
    }
    
    .progress-info {
        flex-direction: column;
        text-align: center;
    }
}

/* Multi-Model Progress */
.multi-model-progress {
    margin-bottom: 2rem;
}

.model-progress-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.model-progress-item {
    background: rgba(248, 249, 250, 0.8);
    padding: 1rem;
    border-radius: 10px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.model-progress-item.training {
    border-color: #3498db;
    background: rgba(52, 152, 219, 0.05);
}

.model-progress-item.completed {
    border-color: #2ecc71;
    background: rgba(46, 204, 113, 0.05);
}

.model-progress-item h4 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-size: 1rem;
}

.model-progress-item .progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.model-progress-item .progress-bar {
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.model-progress-item .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 0%;
    transition: width 0.3s ease;
}

/* Prediction Interface Updates */
.prediction-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.prediction-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.result-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.result-card .result-label {
    display: block;
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 0.5rem;
}

.result-card .result-number {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

.result-trend {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
}

.result-trend.increasing {
    color: #e74c3c;
}

.result-trend.decreasing {
    color: #2ecc71;
}

.result-trend.stable {
    color: #f39c12;
}

.result-alert {
    display: block;
    font-size: 1rem;
    font-weight: bold;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.result-alert.low {
    background: rgba(46, 204, 113, 0.2);
    color: #27ae60;
}

.result-alert.medium {
    background: rgba(243, 156, 18, 0.2);
    color: #e67e22;
}

.result-alert.high {
    background: rgba(231, 76, 60, 0.2);
    color: #c0392b;
}

/* Performance Analysis */
.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.performance-card {
    background: rgba(248, 249, 250, 0.8);
    padding: 1.5rem;
    border-radius: 10px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.performance-card.trained {
    border-color: #2ecc71;
    background: rgba(46, 204, 113, 0.05);
}

.performance-card h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
    text-align: center;
}

.metrics {
    display: grid;
    gap: 0.75rem;
}

.metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metric-label {
    font-weight: 500;
    color: #2c3e50;
}

.metric-value {
    font-weight: bold;
    color: #3498db;
}

.metric-status {
    font-weight: bold;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.metric-status.not-trained {
    background: rgba(149, 165, 166, 0.2);
    color: #7f8c8d;
}

.metric-status.training {
    background: rgba(52, 152, 219, 0.2);
    color: #2980b9;
}

.metric-status.trained {
    background: rgba(46, 204, 113, 0.2);
    color: #27ae60;
}

/* Chart Enhancements */
.chart-wrapper.full-width {
    grid-column: 1 / -1;
}

.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

/* Button Enhancements */
.btn-success {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #27ae60, #229954);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: linear-gradient(135deg, #138496, #117a8b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(52, 152, 219, 0.3);
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Advanced Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: rgba(248, 249, 250, 0.8);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.analytics-card h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
    text-align: center;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.diagnostic-metrics {
    display: grid;
    gap: 0.75rem;
}

.diagnostic-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.diagnostic-label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
}

.diagnostic-value {
    font-weight: bold;
    color: #3498db;
    font-size: 1rem;
}

.insights-list, .recommendations-list {
    display: grid;
    gap: 0.75rem;
}

.insight-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.insight-icon {
    font-size: 1.2rem;
    margin-right: 0.75rem;
}

.insight-text {
    color: #2c3e50;
    font-size: 0.9rem;
    line-height: 1.4;
}

.recommendation-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.recommendation-priority {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-right: 0.75rem;
    min-width: 60px;
    text-align: center;
}

.recommendation-priority.low {
    background: rgba(46, 204, 113, 0.2);
    color: #27ae60;
}

.recommendation-priority.medium {
    background: rgba(243, 156, 18, 0.2);
    color: #e67e22;
}

.recommendation-priority.high {
    background: rgba(231, 76, 60, 0.2);
    color: #c0392b;
}

.recommendation-text {
    color: #2c3e50;
    font-size: 0.9rem;
    line-height: 1.4;
    flex: 1;
}

/* Enhanced Chart Styling */
.chart-wrapper {
    position: relative;
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chart-wrapper:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.chart-wrapper.full-width {
    grid-column: 1 / -1;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(52, 152, 219, 0.3);
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background: #2ecc71;
    box-shadow: 0 0 8px rgba(46, 204, 113, 0.5);
}

.status-indicator.training {
    background: #f39c12;
    box-shadow: 0 0 8px rgba(243, 156, 18, 0.5);
    animation: pulse 2s infinite;
}

.status-indicator.offline {
    background: #95a5a6;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Updates */
@media (max-width: 768px) {
    .model-progress-grid {
        grid-template-columns: 1fr;
    }

    .prediction-summary {
        grid-template-columns: repeat(2, 1fr);
    }

    .performance-grid {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .prediction-controls {
        flex-direction: column;
        align-items: center;
    }

    .diagnostic-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .recommendation-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
