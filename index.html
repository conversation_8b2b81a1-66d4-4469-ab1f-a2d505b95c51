<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heat Exchanger Fouling Prediction System</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>
</head>
<body>
    <header>
        <h1>Heat Exchanger Fouling Prediction System</h1>
        <p>Data-driven maintenance predictions for petroleum refinery operations</p>
    </header>

    <main class="container">
        <!-- Data Upload Section -->
        <section id="data-upload" class="section">
            <h2>Data Upload</h2>
            <div class="upload-area">
                <div class="file-upload">
                    <label for="csv-file" class="upload-label">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">
                            <strong>Choose CSV File</strong>
                            <span>or drag and drop here</span>
                        </div>
                    </label>
                    <input type="file" id="csv-file" accept=".csv" multiple>
                </div>
                <div class="upload-status" id="upload-status"></div>
            </div>
            
            <!-- Data Preview -->
            <div id="data-preview" class="data-preview" style="display: none;">
                <h3>Data Preview</h3>
                <div class="data-stats" id="data-stats"></div>
                <div class="table-container">
                    <table id="preview-table">
                        <thead id="preview-header"></thead>
                        <tbody id="preview-body"></tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Model Configuration Section -->
        <section id="model-config" class="section">
            <h2>Model Configuration</h2>
            <div class="config-grid">
                <div class="config-group">
                    <h3>Architecture</h3>
                    <div class="form-group">
                        <label for="hidden-layers">Hidden Layers:</label>
                        <select id="hidden-layers">
                            <option value="3" selected>3 layers</option>
                            <option value="4">4 layers</option>
                            <option value="2">2 layers</option>
                            <option value="5">5 layers</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="neurons-per-layer">Neurons per Layer:</label>
                        <select id="neurons-per-layer">
                            <option value="64,32,16" selected>64, 32, 16</option>
                            <option value="128,64,32">128, 64, 32</option>
                            <option value="32,16,8">32, 16, 8</option>
                            <option value="64,32,16,8">64, 32, 16, 8</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="activation">Activation Function:</label>
                        <select id="activation">
                            <option value="relu" selected>ReLU</option>
                            <option value="sigmoid">Sigmoid</option>
                            <option value="linear">Linear</option>
                        </select>
                    </div>
                </div>

                <div class="config-group">
                    <h3>Training Parameters</h3>
                    <div class="form-group">
                        <label for="optimizer">Optimizer:</label>
                        <select id="optimizer">
                            <option value="adam" selected>Adam</option>
                            <option value="sgd">SGD</option>
                            <option value="rmsprop">RMSprop</option>
                            <option value="adagrad">Adagrad</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="learning-rate">Learning Rate:</label>
                        <input type="number" id="learning-rate" value="0.001" step="0.0001" min="0.0001" max="0.1">
                    </div>
                    <div class="form-group">
                        <label for="epochs">Epochs:</label>
                        <input type="number" id="epochs" value="100" min="50" max="500">
                    </div>
                    <div class="form-group">
                        <label for="batch-size">Batch Size:</label>
                        <select id="batch-size">
                            <option value="32" selected>32</option>
                            <option value="16">16</option>
                            <option value="64">64</option>
                            <option value="128">128</option>
                        </select>
                    </div>
                </div>

                <div class="config-group">
                    <h3>Regularization</h3>
                    <div class="form-group">
                        <label for="l2-regularization">L2 Regularization:</label>
                        <input type="number" id="l2-regularization" value="0.01" step="0.001" min="0" max="0.1">
                    </div>
                    <div class="form-group">
                        <label for="dropout-rate">Dropout Rate:</label>
                        <input type="number" id="dropout-rate" value="0.2" step="0.1" min="0" max="0.5">
                    </div>
                    <div class="form-group">
                        <label for="loss-function">Loss Function:</label>
                        <select id="loss-function">
                            <option value="mse" selected>Mean Squared Error</option>
                            <option value="mae">Mean Absolute Error</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>

        <!-- Training Dashboard -->
        <section id="training-dashboard" class="section">
            <h2>Training Dashboard</h2>
            <div class="training-controls">
                <button id="start-training" class="btn btn-primary" disabled>Start Training</button>
                <button id="stop-training" class="btn btn-secondary" disabled>Stop Training</button>
                <button id="reset-model" class="btn btn-danger">Reset Model</button>
            </div>
            
            <div class="training-progress">
                <div class="progress-info">
                    <span id="current-epoch">Epoch: 0/0</span>
                    <span id="training-loss">Loss: --</span>
                    <span id="training-accuracy">Accuracy: --</span>
                </div>
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill"></div>
                </div>
            </div>

            <div class="charts-container">
                <div class="chart-wrapper">
                    <canvas id="loss-chart"></canvas>
                </div>
                <div class="chart-wrapper">
                    <canvas id="accuracy-chart"></canvas>
                </div>
            </div>
        </section>

        <!-- Prediction Interface -->
        <section id="prediction-interface" class="section">
            <h2>Prediction Interface</h2>
            <div class="prediction-inputs">
                <h3>Current Parameters</h3>
                <div class="input-grid">
                    <div class="form-group">
                        <label for="unit-charge">Unit Charge (%):</label>
                        <input type="number" id="unit-charge" value="60" min="50" max="100" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="shell-flow">Shell Flow (kg/h):</label>
                        <input type="number" id="shell-flow" value="11000" min="0" step="1">
                    </div>
                    <div class="form-group">
                        <label for="shell-temp-in">Shell Temp In (°C):</label>
                        <input type="number" id="shell-temp-in" value="450" min="0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="shell-temp-out">Shell Temp Out (°C):</label>
                        <input type="number" id="shell-temp-out" value="300" min="0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="tube-flow">Tube Flow (kg/h):</label>
                        <input type="number" id="tube-flow" value="11000" min="0" step="1">
                    </div>
                    <div class="form-group">
                        <label for="tube-temp-in">Tube Temp In (°C):</label>
                        <input type="number" id="tube-temp-in" value="150" min="0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="tube-temp-out">Tube Temp Out (°C):</label>
                        <input type="number" id="tube-temp-out" value="320" min="0" step="0.1">
                    </div>
                </div>
                <button id="generate-prediction" class="btn btn-primary" disabled>Generate Prediction</button>
            </div>
            
            <div id="prediction-results" class="prediction-results" style="display: none;">
                <h3>Prediction Results</h3>
                <div class="result-value">
                    <span class="result-label">Fouling Resistance:</span>
                    <span id="fouling-resistance" class="result-number">-- m² °C/W</span>
                </div>
                <div class="chart-wrapper">
                    <canvas id="prediction-chart"></canvas>
                </div>
            </div>
        </section>
    </main>

    <script src="neural-network.js"></script>
    <script src="data-processor.js"></script>
    <script src="app.js"></script>
</body>
</html>
